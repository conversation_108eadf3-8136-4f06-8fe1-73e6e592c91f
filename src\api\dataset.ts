import type { IPage } from '@/interface';
import type { ICreateDataset, IFetchMyDataset, IUpdateDatasetField, exportFormatType } from '@/interface/dateset';
import request from '@/utils/request';

export function fetchMyDatasetList(data: IFetchMyDataset) {
  return request.$Axios.get(`/dataset/all`, data);
}
export function fetchPublicDatasetList(data: IFetchMyDataset) {
  return request.$Axios.get(`/dataset/public`, data);
}

export function createDataset(data: ICreateDataset) {
  return request.$Axios.post(`/dataset/create`, data);
}

export function deleteDataset(dataset_id: string) {
  return request.$Axios.del(`/dataset/${dataset_id}`);
}
export function deleteDatasetItems(dataset_id: string, index: number) {
  return request.$Axios.del(`/dataset/${dataset_id}/items/${index}`);
}
export function publishDataset(dataset_id: string) {
  return request.$Axios.post(`/dataset/${dataset_id}/publish`);
}
export function unpublishDataset(dataset_id: string) {
  return request.$Axios.post(`/dataset/${dataset_id}/unpublish`);
}
export function featchDatasetOverview(dataset_id: string) {
  return request.$Axios.get(`/dataset/${dataset_id}/overview`);
}
export function featchDatasetItems(dataset_id: string, data?: IPage) {
  return request.$Axios.get(`/dataset/${dataset_id}/items`, data);
}
export function updateDatasetItems(dataset_id: string, index: number, data: Record<string, string | number>) {
  return request.$Axios.put(`/dataset/${dataset_id}/items/${index}`, { data });
}
export function insertDatasetItems(dataset_id: string, data: Record<string, string | number>) {
  return request.$Axios.post(`/dataset/${dataset_id}/items`, { data });
}
export function exportDataset(dataset_id: string, export_format: exportFormatType) {
  return request.$Axios.post(`/dataset/${dataset_id}/export?export_format=${export_format}`);
}
export function updateDatasetField(dataset_id: string, data: IUpdateDatasetField) {
  return request.$Axios.put(`/dataset/${dataset_id}/field`, data);
}
export function updateDataset(dataset_id: string, data: { name: string; summary?: string; source: string }) {
  return request.$Axios.put(`/dataset/${dataset_id}/metadata`, data);
}
export function delDatasetField(dataset_id: string, field_name: string) {
  return request.$Axios.del(`/dataset/${dataset_id}/fields/${field_name}`);
}