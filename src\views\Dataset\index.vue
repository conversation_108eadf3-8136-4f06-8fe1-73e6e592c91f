<script setup lang="ts">
  import { getLocalItem } from '@/utils';
  import { administratorIds } from '@/utils/enum';
  import { ref, computed } from 'vue';
  import List from '@/views/Dataset/Components/list.vue';
  import PublicList from '@/views/Dataset/Components/publicList.vue';
  import { useRoute } from 'vue-router';
  const route = useRoute();
  const { userId } = JSON.parse(getLocalItem('HQSK_AI_PLATFORM_FRONTEND_USERINFO') || '{}');
  const isAdministrator = computed(() => administratorIds.includes(userId));
  const activeKey = ref('my');
</script>

<template>
  <template v-if="route.name === 'Dataset'">
    <template v-if="isAdministrator">
      <List />
    </template>
    <template v-else>
      <a-tabs v-model:active-key="activeKey" type="card">
        <a-tab-pane key="my" tab="我的数据集">
          <List />
        </a-tab-pane>
        <a-tab-pane key="fields" tab="公共数据集">
          <PublicList />
        </a-tab-pane>
      </a-tabs>
    </template>
  </template>
  <template v-else>
    <router-view></router-view>
  </template>
</template>

<style scoped lang="less"></style>
