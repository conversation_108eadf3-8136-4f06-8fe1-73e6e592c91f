import type { IFetchAgentList, IMessage, IUpdateAgentProps, ILikeList, IPublishAgentProps, IPublishConfig } from '@/interface/agent';
import request from '@/utils/request';

export function fetchAgentList(data: IFetchAgentList) {
  return request.$Axios.get(`/agent/version/latest/list`, data);
}
// 创建智能体
export const createAgent = () => {
  return request.$Axios.post(`/agent/version/add`);
};
// 智能体详情
export const agentVersionDetail = (version_id: string) => {
  return request.$Axios.get(`/agent/version/latest/detail/${version_id}`);
};
// 发布的智能体详情
export const getAgentDetail = (version_id: string, noToLogin: boolean) => {
  return request.$Axios.get(`/agent/detail/${version_id}`, { noToLogin });
};
// 编辑智能体
export const updateAgent = (version_id: string, data: IUpdateAgentProps) => {
  return request.$Axios.post(`/agent/version/update/${version_id}`, data);
};

// 获取某个智能体详情列表
export const getAgentPublishDetail = (version_id: string) => {
  return request.$Axios.get(`agent/version/latest/detail/${version_id}`);
};

// 更新智能体发布
export const updateAgentPublish = (version_id: string, data: IPublishConfig) => {
  return request.$Axios.post(`/agent/version/${version_id}/publish-config`, data);
};

// 发布智能体
export const publishAgent = (version_id: string) => {
  return request.$Axios.post(`/agent/version/publish/${version_id}`);
};
// 更新发布渠道
export const publishChannel = (version_id: string, params?: IPublishAgentProps) => {
  return request.$Axios.post(`/agent/version/${version_id}/publish-config`, params);
};
// 智能体对话
export const agentChct = (agent_id: string, data: { question: string; history: IMessage[] }) => {
  return request.$Axios.post(`/agent/chat/${agent_id}`, data);
};
// 删除智能体
export const delAgent = (version_id: string) => {
  return request.$Axios.del(`/agent/version/delete/${version_id}`);
};
// 检查智能体名称是否重复
export const checkAgentName = (name: string) => {
  return request.$Axios.post(`/agent/name/check`, { name });
};

export const getDebuggerFollowupQuestion = (
  version_id: string,
  data: { history: { role: string; content: string }[] },
) => {
  return request.$Axios.post(`/agent/debug/followup/question/${version_id}`, data);
};
export const getFollowupQuestion = (version_id: string, data: { history: { role: string; content: string }[] }) => {
  return request.$Axios.post(`/agent/followup/question/${version_id}`, data);
};

// 智能体问答结果正向反馈
export const getLike = (agent_id: string, data: ILikeList) => {
  return request.$Axios.post(`agent/response/feedback/${agent_id}`, data);
};

// 智能体问答结果负向反馈
export const getDisLike = (version_id: string, data: ILikeList) => {
  return request.$Axios.post(`agent/response/feedback/${version_id}`, data);
};
// 获取聊天历史列表
export const getChatHistoryList = (agent_id: string, noToLogin: boolean) => {
  return request.$Axios.get(`/agent/chat/${agent_id}/history/all`, { noToLogin });
};

// 删除历史记录
export const delChatData = (id: string) => {
  return request.$Axios.del(`/agent/chat/history/${id}`);
};
