<script setup lang="ts">
  import { CustomForm } from '@/components';
  import type { IFormItem, IPage } from '@/interface';
  import { administratorIds } from '@/utils/enum';
  import { TABLE_PAGINATION } from '@/json/common';
  import type { ColumnType, TablePaginationConfig } from 'ant-design-vue/es/table';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import { ref, reactive, onMounted, computed } from 'vue';
  import { convertIsoTimeToLocalTime, getLocalItem } from '@/utils/common';
  import { fetchMyDatasetList, deleteDataset, publishDataset, updateDataset } from '@/api/dataset';
  import { useRouter } from 'vue-router';
  import type { IDatasetItems } from '@/interface/dateset';
  import {
    DATASET_PUBLISH_STATE_MAP,
    DATASET_IMPORT_STATE_MAP,
    datasetTypes,
    import_status,
    publish_status,
    dataSourceOptions,
  } from '.';
  import { message } from 'ant-design-vue';
  import type { Rule } from 'ant-design-vue/es/form';
  const router = useRouter();
  const loading = ref(false);
  const { userId } = JSON.parse(getLocalItem('HQSK_AI_PLATFORM_FRONTEND_USERINFO') || '{}');
  const isAdministrator = computed(() => administratorIds.includes(userId));
  const DEFAULT_SEARCHSTATE = {
    name: undefined,
    type: undefined,
    import_status: undefined,
    publish_status: undefined,
    source: undefined,
  };
  const searchState = reactive({ ...DEFAULT_SEARCHSTATE });
  const tableHeight = ref(0);
  const delVisible = ref(false);
  const editVisible = ref(false);
  const updateFormRef = ref();
  // @ts-expect-error
  const currentRecord = reactive<IDatasetItems>({});
  const formConfig: IFormItem[] = [
    {
      field: 'name',
      type: 'input',
      label: '数据集名称',
      placeholder: '请输入',
    },
    {
      field: 'type',
      type: 'select',
      label: '数据集类型',
      options: datasetTypes,
      placeholder: '请输入',
    },
    {
      field: 'import_status',
      type: 'select',
      label: '导入状态',
      options: [
        { label: '导入中', value: 'importing' },
        { label: '成功', value: 'imported' },
        { label: '失败', value: 'failed' },
      ],
      placeholder: '请输入',
    },
    {
      field: 'publish_status',
      type: 'select',
      label: '发布状态',
      options: [
        { label: '未发布', value: 'unpublished' },
        { label: '成功', value: 'published' },
        { label: '失败', value: 'failed' },
      ],
      placeholder: '请输入',
    },
    {
      field: 'source',
      type: 'select',
      label: '数据来源',
      options: dataSourceOptions,
      placeholder: '请输入',
    },
  ];
  const columns: ColumnType[] = [
    { title: '数据集名称', dataIndex: 'name', fixed: 'left' },
    { title: '简介', dataIndex: 'summary', width: 180, ellipsis: true },
    { title: '数据集类型', dataIndex: 'type' },
    { title: '数据集来源', dataIndex: 'source' },
    { title: '数据量', dataIndex: 'data_quantity', width: 100 },
    { title: '数据大小', dataIndex: 'file_size', width: 100 },
    { title: '导入状态', dataIndex: 'import_status' },
    { title: '发布状态', dataIndex: 'publish_status' },
    // { title: '创建人', dataIndex: 'source_path' },
    { title: '创建时间', dataIndex: 'created_at' },
    { title: '发布时间', dataIndex: 'published_at', width: 180 },
    { title: '操作', dataIndex: 'operation', fixed: 'right', width: 140 },
  ];
  const pagination = reactive({ ...TABLE_PAGINATION });
  const pageParame: IPage = reactive({ page: 1, limit: 10 });
  const dataSource = reactive<IDatasetItems[]>([]);

  const getList = async () => {
    loading.value = true;
    try {
      const { page, total_count, items } = (await fetchMyDatasetList({ ...pageParame, ...searchState })) as IPage & {
        total_count: number;
        items: IDatasetItems[];
      };
      dataSource.length = 0;
      dataSource.push(...items);
      Object.assign(pagination, { current: page, total: total_count });
      loading.value = false;
    } catch {
      loading.value = false;
    }
  };
  const handleAdd = () => {
    router.push('/dataset/add');
  };

  const getTableHeight = () => {
    const tableItem = document.querySelector('.container');
    tableHeight.value = tableItem?.clientHeight as number;
  };
  const onFinish = (values: Record<string, string>) => {
    const searchConfig: { [key: string]: string } = {};
    for (let key in values) {
      searchConfig[key] = values[key];
    }
    Object.assign(searchState, { ...searchConfig });
    pageParame.page = 1;
    getList();
  };
  const onRest = () => {
    Object.assign(pagination, { ...TABLE_PAGINATION });
    Object.assign(pageParame, { page: 1, limit: 10 });
    Object.assign(searchState, { ...DEFAULT_SEARCHSTATE });
    getList();
  };

  const toggleTable = (_pagination: TablePaginationConfig) => {
    let { current, pageSize } = _pagination;
    console.log(current, pageSize);
    Object.assign(pagination, { current, pageSize });
    Object.assign(pageParame, { page: current, limit: pageSize });
    getList();
  };
  const serverInfo = (id: string) => {
    router.push({
      path: `/dataset/detail/${id}`,
      query: {
        source: 'all',
      },
    });
  };
  const handlePublish = async (id: string) => {
    await publishDataset(id);
    message.success('发布成功');
    getList();
  };
  const handleDelete = (record: IDatasetItems) => {
    Object.assign(currentRecord, record);
    delVisible.value = true;
  };
  const handleEdit = (record: IDatasetItems) => {
    Object.assign(currentRecord, record);
    editVisible.value = true;
  };
  const confirmDelete = async () => {
    await deleteDataset(currentRecord.id);
    message.success('已删除');
    delVisible.value = false;
    getList();
  };
  const validatorName = (_rule: Rule, value: string) => {
    if (value == '' || value.trim().length == 0) {
      return Promise.reject('请输入数据集名称');
    }
    const reg = /^[a-zA-Z0-9][a-zA-Z0-9_-]*$/;
    if (!reg.test(value)) {
      return Promise.reject(
        '模型名称仅支持 英文（大小写）、数字、中划线 -、下划线 _，且不能以下划线 _ 或中划线 - 开头',
      );
    }
    if (value.length < 2 || value.length > 64) {
      return Promise.reject('模型名称请输入2-64个字');
    }
    return Promise.resolve();
  };
  const confirmEdit = async () => {
    await updateFormRef.value.validateFields();
    const { name, source, summary } = currentRecord;
    await updateDataset(currentRecord.id, { name, source, summary });
    editVisible.value = false;
    getList();
  };
  onMounted(() => {
    getTableHeight();
    getList();
  });
</script>

<template>
  <CustomForm style="margin-bottom: 0" :form-items="formConfig" @on-finish="onFinish" @on-rest="onRest" />
  <div class="table-button">
    <a-button type="primary" @click="handleAdd"><PlusOutlined />创建数据集</a-button>
  </div>
  <a-table
    :data-source="dataSource"
    :columns="columns"
    :pagination="pagination"
    :loading="loading"
    :scroll="{ y: isAdministrator ? tableHeight - 280 : tableHeight - 330, x: 'max-content' }"
    @change="toggleTable"
  >
    <template #bodyCell="{ column, record, text }">
      <div v-if="column.dataIndex === 'operation'" class="operation-box">
        <template v-if="record.import_status === DATASET_IMPORT_STATE_MAP.IMPORTED">
          <a @click="serverInfo(record.id)">查看</a>
          <a-popconfirm
            v-if="
              [DATASET_PUBLISH_STATE_MAP.UNPUBLISHED, DATASET_PUBLISH_STATE_MAP.FAILED].includes(record.publish_status)
            "
            title="确定发布这个数据集吗?"
            @confirm="handlePublish(record.id)"
          >
            <a>发布</a>
          </a-popconfirm>
          <a
            v-if="
              [DATASET_PUBLISH_STATE_MAP.UNPUBLISHED, DATASET_PUBLISH_STATE_MAP.FAILED].includes(record.publish_status)
            "
            @click="handleEdit(record)"
            >编辑</a
          >
        </template>
        <a class="del-btn" @click="handleDelete(record)">删除</a>
      </div>
      <div v-else-if="['created_at', 'published_at'].includes(column.dataIndex)">
        {{ convertIsoTimeToLocalTime(text) }}
      </div>
      <div v-else-if="column.dataIndex === 'name'">
        <div>{{ record.name }}</div>
        <div class="text-#797979">{{ record.id }}</div>
      </div>
      <div v-else-if="column.dataIndex === 'type'">
        {{ datasetTypes.find((item) => item.value === text)?.label }}
      </div>
      <div v-else-if="column.dataIndex === 'source'">
        {{ dataSourceOptions.find((item) => item.value === text)?.label }}
      </div>
      <div v-else-if="column.dataIndex === 'import_status'">
        <a-popover v-if="DATASET_PUBLISH_STATE_MAP.FAILED">
          <template #content>{{ record.import_error_msg || '--' }}</template>
          <a-tag :color="import_status.find((item) => item.value === text)?.color">{{
            import_status.find((item) => item.value === text)?.label
          }}</a-tag>
        </a-popover>
        <a-tag v-else :color="import_status.find((item) => item.value === text)?.color">{{
          import_status.find((item) => item.value === text)?.label
        }}</a-tag>
      </div>
      <div v-else-if="column.dataIndex === 'publish_status'">
        <a-popover v-if="DATASET_PUBLISH_STATE_MAP.FAILED">
          <template #content>{{ record.publish_error_msg || '--' }}</template>
          <a-tag :color="publish_status.find((item) => item.value === text)?.color">{{
            publish_status.find((item) => item.value === text)?.label
          }}</a-tag>
        </a-popover>
        <a-tag v-else :color="publish_status.find((item) => item.value === text)?.color">{{
          publish_status.find((item) => item.value === text)?.label
        }}</a-tag>
      </div>
      <div v-else>{{ text || '--' }}</div>
    </template>
  </a-table>
  <a-modal v-model:open="delVisible" centered :title="`确定删除数据集“${currentRecord.name}”？`" @ok="confirmDelete">
    <p>
      数据集已被用于模型训练或评测任务，删除可能导致任务失败或数据不一致，请确保相关任务已完成或备份数据后再进行操作。确定删除数据集吗？
    </p>
  </a-modal>
  <a-modal v-model:open="editVisible" width="50%" centered title="编辑数据集" @ok="confirmEdit">
    <a-form ref="updateFormRef" layout="vertical" autocomplete="off" :model="currentRecord">
      <a-form-item label="数据集名称" name="name" :rules="[{ required: true, validator: validatorName }]">
        <a-input v-model:value="currentRecord.name" style="width: 100%" show-count :maxlength="64" />
      </a-form-item>
      <a-form-item label="简介" name="summary">
        <a-textarea
          v-model:value="currentRecord.summary"
          allow-clear
          show-count
          :maxlength="500"
          :auto-size="{ minRows: 2, maxRows: 6 }"
          placeholder="请输入数据集简介"
        />
      </a-form-item>
      <a-form-item label="数据来源" name="source" :rules="[{ required: true }]">
        <a-select v-model:value="currentRecord.source" placeholder="请选择数据来源" style="width: 100%">
          <a-select-option v-for="opt in dataSourceOptions" :key="opt.value" :value="opt.value">{{
            opt.label
          }}</a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<style scoped lang="less">
  .table-button {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 20px;
  }
  :deep(.ant-table-cell-ellipsis div) {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
</style>
