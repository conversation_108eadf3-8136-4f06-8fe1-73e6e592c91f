import request from '@/utils/request';
import { getEnv } from '@/utils';

const { VITE_APP_AVATAR_URL } = getEnv();

const AVATAR = VITE_APP_AVATAR_URL;

// 定义宽松的对象类型
type TLooseObject = Record<string, unknown>;

// 上传视频
export function UploadVideo(params: FormData, config: TLooseObject) {
  return request.$Axios
    .post(`/virtual-classroom-service/face/video/upload/[${AVATAR}]`, params, config)
    .then((data) => data as string[]);
}

// 获取数字人列表
export function getDigitalHumans(params?: TLooseObject) {
  return request.$Axios.get(`/virtual-classroom-service/face/all/[${AVATAR}]`, params);
}

// 预览
export function getDigitalHumansPreview(params?: TLooseObject) {
  return request.$Axios.post(`/virtual-classroom-service/face/video/preview/[${AVATAR}]`, params);
}

// 开始生成数字人
export function generateDigitalHumans(params?: TLooseObject) {
  return request.$Axios.post(`/virtual-classroom-service/face/video/pretrain/[${AVATAR}]`, params);
}

// 删除数字人
export function delDigitalHumans(params?: TLooseObject) {
  return request.$Axios.del(`/virtual-classroom-service/face/video/pretrain/[${AVATAR}]`, params);
}

// 重命名数字人
export function reNameDigitalHumans(params?: TLooseObject) {
  return request.$Axios.put(`/virtual-classroom-service/face/all/[${AVATAR}]`, params);
}

// 数字人重新生成
export function reGenerateDigitalHumans(params?: TLooseObject) {
  return request.$Axios.put(`virtual-classroom-service/face/video/pretrain/[${AVATAR}]`, params);
}
