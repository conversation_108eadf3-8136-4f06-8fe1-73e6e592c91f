
<script setup lang="ts">
  import Icon from '@/components/Icon/index.vue';
  import { defineEmits } from 'vue';
  const props = defineProps<{ visible: boolean }>();
  const emit = defineEmits(['close', 'select']);

  function handleCancel() {
    emit('close');
  }
  function selectMode(mode: 'video' | 'image') {
    emit('select', mode);
  }
</script>
<template>
  <a-modal
    :open="visible"
    title="选择创建模式"
    :footer="null"
    :width="800"
    centered
    @cancel="handleCancel"
  >
    <div class="mode-modal">
      <div class="divider"></div>
      <div class="mode-select-modal">

        <div class="mode-option" @click="selectMode('video')">
          <!-- <div class="icon video"><i class="iconfont icon-video"></i></div> -->
          <Icon name="shipinshengchengshuziren" :size="52" />
          <div class="info">
            <span class="tag">推荐高精度</span>
            <div class="title">视频生成数字人</div>
            <div class="desc">上传一段视频，精准训练复刻数字人，效果较佳，训练时间约15分钟</div>
          </div>
        </div>
        <div class="mode-option" @click="selectMode('image')">
          <Icon name="bianzhuangshuziren" :size="52" />
          <div class="info">
            <div class="title">变装数字人</div>
            <div class="desc">上传人物照片，完善人物信息，生成变装数字人</div>
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>


<style scoped lang="less">
.mode-modal{
  padding: 0;
  margin: 0;
  .divider {
    height: 1px;
    background-color: #EDEFF2;
    margin: 0 26px;
  }

  .mode-select-modal {
    display: flex;
    flex-direction: column;
    gap: 30px;
    padding: 16px 0;
    margin:  30px 100px;

    .mode-option {
      display: flex;
      align-items: center;
      padding: 24px 26px;
      background: #FFFFFF;
      border-radius: 8px;
      border: 1px solid #EDEFF2;
      position: relative;

      &:hover {
        border: 1px solid #1777ff;
      }


      .info {
        margin-left: 20px;
        .tag {
          position: absolute;
          top: 0px;
          right: 0px;
          background: #D9E9FF;
          padding: 5px 10px 8px 10px;
          border-radius: 0px 8px 0px 8px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 10px;
          color: #1777FF;
          line-height: 14px;
          text-align: left;
          font-style: normal;
        }
        .title {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: #17181A;
          line-height: 20px;
          text-align: left;
          font-style: normal;
          margin-bottom: 8px;

        }
        .desc {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #636466;
          line-height: 16px;
          text-align: left;
          font-style: normal;
        }
      }
    }
  }
}
</style>
