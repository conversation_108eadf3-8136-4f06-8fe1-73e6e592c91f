<!--
 * @Author: dengfusheng 
 * @Date: 2025-02-26 11:16:53
 * @LastEditTime: 2025-03-28 10:11:58
 * @LastEditors: <EMAIL>
 * @FilePath: \ai-platform\ai-platform-frontend\src\views\Task\TaskInfo\index.vue
 * @Description: 任务详情主页面
-->
<script setup lang="ts">
  import { nextTick, onBeforeUnmount, reactive, ref, watch, onMounted } from 'vue';
  import { Descriptions, Spin } from 'ant-design-vue';
  import { QuestionCircleOutlined } from '@ant-design/icons-vue';
  import Monitor from '@/views/Task/TaskInfo/Monitor.vue';
  import ModelTarget from '@/views/Task/TaskInfo/ModelTarget.vue';
  import SklearnModelTarget from '@/views/Task/TaskInfo/sklearnModelTarget.vue';
  import RegressMonitor from '@/views/Task/TaskInfo/regressMonitor.vue';
  import ClusterMonitor from '@/views/Task/TaskInfo/clusterMonitor.vue';
  import { getOutputModelDetail, getSKlearnTaskLogs, getSklearnTaskInfo, getTaskInfo, getTaskLogs } from '@/api/model';
  import { classifyCategory, regressCategory, clusterCategory } from '@/views/Model/Manage/index';
  import { useRoute } from 'vue-router';
  import {
    trainMethods,
    trainTypes,
    general_parameters,
    other_parameters,
    partial_parameters,
    parameter,
    unsloth_parameters,
    sklearn_svc_parameters,
    sklearn_linearRegression_parameters,
    sklearn_KMeans_parameters,
    GPUList,
  } from '@/views/Model/Train/index.ts';
  import { TrainStatus } from '../index.js';
  import { DownOutlined, UpOutlined } from '@ant-design/icons-vue';
  import { VAceEditor } from 'vue3-ace-editor';
  import 'ace-builds/src-noconflict/theme-chrome';
  import 'ace-builds/src-noconflict/theme-monokai.js';
  import 'ace-builds/src-noconflict/mode-python.js';
  import 'ace-builds/src-noconflict/mode-sh.js';
  import { formatTime, getLocalItem, setLocalItem } from '@/utils/common.js';
  import Deploy from '@/views/Model/Deploy/index.vue';
  import type { IAceOptions } from '@/interface';
  import type { IDatasetParameter } from '@/interface/model';

  const route = useRoute();
  const runCommand = ref(``);
  const aceEditor = ref(null);

  const deployState = reactive({
    visible: route.query.type === 'deploy',
    loading: false,
  });

  interface ITaskInfo {
    task_name: string;
    id: string;
    category: string;
    description: string;
    status: string;
    run_time: number;
    train_parameter: string;
    output_name: string;
    model_name: string;
    // image_info: { name }:string;
    tf_parameter?: { type: string; finetuning_type: string; stage: string };
    // method_parameter: { finetuning_type: string; stage: string };
    docker_image: string;
    docker_images: { id: string; name: string }[];
    run_command: string[];
    created_at: string;
    output_model_id?: string;
    gpu_number_list?: number[];
    dataset_parameter?: IDatasetParameter;
  }
  interface IState {
    langEnum: string[];
    activeKey: number;
    hyperparameter: string[][];
    taskInfo: ITaskInfo;
    expand: boolean;
    logStr: string;
    basicInfo: string[][];
    spinning: boolean;
    finished?: boolean;
    editor: unknown;
  }
  interface IModelInfo {
    deploy_method: string;
    id: string;
    gpu_number_list?: string;
    category?: string;
    finetuning_type: number;
    service_name: string;
    introduction: string;
    container_count: number;
    run_command: string;
    docker_image_id: string;
    dataset_parameter: IDatasetParameter;
  }
  const modelInfo = reactive<IModelInfo>({
    deploy_method: '',
    id: '',
    gpu_number_list: undefined,
    category: '',
    finetuning_type: 0,
    service_name: '',
    introduction: '',
    container_count: 1,
    run_command: '',
    docker_image_id: '',
    dataset_parameter: { name: undefined, type: 'system', numeric_features: [], numeric_labels: [] },
  });

  const state = reactive<IState>({
    langEnum: ['SQL', 'Python', 'sh'],
    activeKey: getLocalItem('taskinfotab') * 1 || 1,
    hyperparameter: [],
    taskInfo: {
      task_name: '',
      id: '',
      category: '',
      description: '',
      dataset_parameter: { name: undefined, type: undefined, numeric_features: [], numeric_labels: [] },
      status: '',
      run_time: 0,
      train_parameter: '',
      output_name: '',
      model_name: '',
      docker_image: '',
      docker_images: [],
      // image_info: { name }:'',
      tf_parameter: { type: '', finetuning_type: '', stage: '' },
      run_command: [],
      created_at: '',
    },
    expand: true,
    logStr: '',
    basicInfo: [
      ['任务名/ID', ''],
      ['预训练模型', ''],
      ['训练方式', ''],
      ['运行状态', ''],
      ['运行时长', ''],
      ['输出模型名', ''],
    ],
    spinning: false,
    finished: undefined,
    editor: undefined,
  });

  const aceOptions: IAceOptions = {
    enableBasicAutocompletion: true,
    enableSnippets: true,
    enableLiveAutocompletion: true,
    tabSize: 4,
    enableEmmet: true,
    fontSize: 15,
    showPrintMargin: false, // 去除编辑器里的竖线
    highlightActiveLine: true,
    useWorker: true,
    readOnly: false,
  };
  const parameters = [
    ...sklearn_svc_parameters,
    ...sklearn_linearRegression_parameters,
    ...sklearn_KMeans_parameters,
    ...unsloth_parameters,
    ...general_parameters,
    ...other_parameters,
    ...partial_parameters,
    ...parameter.LoRA_parameters,
    ...parameter.RLHF_parameters,
    ...parameter.GaLore_parameters,
    ...parameter.BAdam_parameters,
  ];
  const dataset_parameter_map: Record<string, string> = {
    identity: '身份意识微调数据集',
    travel_plan: '环球数科文旅垂类数据集',
    unsloth_demo: '训练数据集示例',
  };

  const getModelInfo = async (id: string) => {
    const data = await getOutputModelDetail(id);
    state.taskInfo.docker_images = data.docker_images;
    state.taskInfo.dataset_parameter = data.dataset_parameter;
    Object.assign(modelInfo, data);
  };
  onMounted(async () => {
    const { taskid, output_model_id, model_category } = route.query;
    console.log(taskid, 'taskid');
    // 获取模型详情 获取模型镜像数据
    if (output_model_id) {
      await getModelInfo(String(output_model_id));
    }
    if (taskid) {
      state.spinning = true;
      state.taskInfo =
        model_category === 'sklearn' ? await getSklearnTaskInfo(String(taskid)) : await getTaskInfo(taskid as string);
      console.log('🚀 ~ getSklearnTaskInfo:', state.taskInfo);
      const {
        task_name,
        id,
        description,
        dataset_parameter,
        status,
        run_time,
        train_parameter,
        output_name,
        model_name,
        // image_info: { name },
        // method_parameter: { finetuning_type, stage },
        tf_parameter,
        run_command,
        gpu_number_list,
      } = state.taskInfo;
      if (run_command) {
        run_command.forEach((str: string) => {
          runCommand.value += `${str}
  `;
        });
      }
      // @ts-expect-error
      state.basicInfo =
        route.query.model_category === 'sklearn'
          ? [
              ['任务名/ID', task_name || id],
              ['预训练模型', model_name],

              ['数据集名称', dataset_parameter!.name],
              // @ts-expect-error
              ['数据特征', dataset_parameter.numeric_features.join('、')],
              // @ts-expect-error
              ['数据标签', dataset_parameter.numeric_labels ? dataset_parameter.numeric_labels.join('、') : '--'],
              ['运行状态', TrainStatus.find((e) => e.value == status)?.label as string],
              ['运行时长', `${formatTime(run_time)}`],
              ['输出模型名', output_name],
              ['版本描述', description],
            ]
          : [
              ['任务名/ID', task_name || id],
              ['预训练模型', model_name],
              ['训练框架', tf_parameter ? tf_parameter!.type : '--'],
              [
                '训练方式',
                tf_parameter
                  ? `${trainTypes.find((e) => e.value == tf_parameter!.finetuning_type)!.label} -- ${trainMethods.find((e) => e.value == tf_parameter!.stage)!.name}`
                  : '--',
              ],
              [
                '所选数据集',
                dataset_parameter
                  ? `${dataset_parameter!.type === 'system' ? '公开数据集' : '自研数据集'} -- ${dataset_parameter_map[dataset_parameter!.name!]}`
                  : '--',
              ],
              ['运行状态', TrainStatus.find((e) => e.value == status)?.label as string],
              ['运行时长', `${formatTime(run_time)}`],
              ['输出模型名', output_name],
              [
                '显卡信息',
                gpu_number_list
                  ? GPUList.filter((item) => gpu_number_list.includes(item.value as number))
                      .map((i) => i.label)
                      .join(',')
                  : '--',
              ],
              ['版本描述', description],
            ];
      state.hyperparameter = Object.entries(train_parameter || {});
      state.spinning = false;
    }
  });

  watch(
    [() => state.activeKey, () => state.finished],
    async ([activeKey, _finished]) => {
      const { taskid } = route.query;

      if (activeKey == 2 && taskid && (_finished === undefined || _finished === true)) {
        state.finished = false;
        if (route.query.model_category) {
          const data = await getSKlearnTaskLogs(String(taskid));
          state.logStr = data;
        } else {
          const data = await getTaskLogs(taskid as string);
          state.logStr = data.log;
        }
        nextTick(() => {
          if (state.editor) {
            // @ts-expect-error
            const session = state.editor.getSession();
            const lastLine = session.getLength(); // 获取最后一行
            // @ts-expect-error
            state.editor.gotoLine(lastLine); // 将光标移动到最后一行
            // @ts-expect-error
            state.editor.scrollToLine(lastLine, true, true, () => {}); // 滚动到最后一行
          }
        });
        await new Promise((resolve) => setTimeout(resolve, 1000));
        // if (finished === false) state.finished = true;
      }
      if (activeKey !== 2) state.finished = undefined;
    },
    { deep: true, immediate: true },
  );

  const editorInit = (editor: any) => (state.editor = editor);

  onBeforeUnmount(() => setLocalItem('taskinfotab', ''));
</script>

<template>
  <div class="task-info">
    <div class="flex justify-between items-center">
      {{ state.taskInfo?.task_name || '-------' }}
      <a-button v-if="state.taskInfo?.status === 'completed'" type="primary" @click="deployState.visible = true"
        >部署</a-button
      >
    </div>
    <a-tabs v-model:active-key="state.activeKey" @change="(key: any) => setLocalItem('taskinfotab', key)">
      <a-tab-pane :key="1" tab="任务详情">
        <div class="particulars overflow-scroll">
          <Spin :spinning="state.spinning">
            <div class="textbefo">基本信息</div>
            <Descriptions size="small">
              <Descriptions.Item v-for="(d, l) in state.basicInfo" :key="l" :label="d[0]">
                {{ d[1] || '--' }}
              </Descriptions.Item>
              <!-- <Descriptions.Item /> -->
              <Descriptions.Item label="超参配置" :span="4">
                <div class="details" @click="state.expand = !state.expand">
                  <span>详细信息</span>
                  <DownOutlined v-if="state.expand" />
                  <UpOutlined v-else />
                </div>
              </Descriptions.Item>
            </Descriptions>
            <div :class="{ hyperparameter: true, fewer: !state.expand }">
              <a-descriptions size="small" :column="4" bordered>
                <template v-for="(n, i) in state.hyperparameter" :key="i">
                  <a-descriptions-item
                    v-if="parameters.find((e: any) => e.value == n[0])?.label"
                    :label="
                      ['bf16', 'fp16', 'fp32', 'pure_bf16'].includes(n[0])
                        ? `computed_type 计算配置`
                        : parameters.find((e: any) => e.value == n[0])?.label
                    "
                  >
                    {{
                      n[1] !== undefined ? (['bf16', 'fp16', 'fp32', 'pure_bf16'].includes(n[0]) ? n[0] : n[1]) : '--'
                    }}
                  </a-descriptions-item>
                </template>
              </a-descriptions>
            </div>
            <div class="textbefo">计算资源配置</div>
            <Descriptions size="small" :column="2">
              <Descriptions.Item label="计算资源">{{ '公共资源' }}</Descriptions.Item>
              <Descriptions.Item>
                <Descriptions size="small" :column="1" layout="vertical">
                  <Descriptions.Item label="资源列表">
                    <a-table
                      :columns="[
                        { title: 'GPU', dataIndex: 'gpu' },
                        { title: 'CPU', dataIndex: 'cpu' },
                        { title: '内存', dataIndex: 'storage' },
                      ]"
                      :data-source="[{ key: '1', gpu: '1 * NVIDIA V100', cpu: 'Dynamic', storage: 'Dynamic' }]"
                      bordered
                      :pagination="false"
                    >
                    </a-table>
                  </Descriptions.Item>
                </Descriptions>
              </Descriptions.Item>
            </Descriptions>

            <div class="textbefo">镜像</div>
            <Descriptions size="small" :column="2">
              <Descriptions.Item label="镜像">{{ state.taskInfo.docker_image }}</Descriptions.Item>
            </Descriptions>
          </Spin>
        </div>
      </a-tab-pane>
      <a-tab-pane :key="2" tab="任务日志">
        <v-ace-editor
          ref="aceEditor"
          v-model:value="state.logStr"
          :lang="state.langEnum[2].toLowerCase()"
          theme="monokai"
          :options="aceOptions"
          style="height: 100%; border: 1px solid #dbd3d3"
          @init="editorInit"
        />
      </a-tab-pane>
      <template v-if="route.query.model_category">
        <a-tab-pane :key="4" tab="模型指标监控">
          <div class="carousel">
            <div class="carousel-item">建议监控时长不低于 1 分钟，否则可能无法获取数据</div>
          </div>
          <!-- 分类监控指标 -->
          <SklearnModelTarget
            v-if="classifyCategory.includes(modelInfo.category!)"
            :active-key="state.activeKey"
          ></SklearnModelTarget>
          <!-- 回归监控指标 -->
          <RegressMonitor
            v-if="regressCategory.includes(modelInfo.category!)"
            :active-key="state.activeKey"
          ></RegressMonitor>
          <!-- 聚类监控指标 -->
          <ClusterMonitor
            v-if="clusterCategory.includes(modelInfo.category!)"
            :active-key="state.activeKey"
          ></ClusterMonitor>
        </a-tab-pane>
      </template>
      <template v-else>
        <template v-if="state.taskInfo?.category && !['blender', 'p2l'].includes(state.taskInfo?.category)">
          <a-tab-pane :key="3" tab="任务监控">
            <div class="carousel">
              <div class="carousel-item">建议监控时长不低于 1 分钟，否则可能无法获取数据</div>
            </div>
            <Monitor
              v-if="state.taskInfo"
              :created="state.taskInfo?.created_at"
              :run-time="state.taskInfo?.run_time"
              :status="state.taskInfo?.status"
            >
            </Monitor>
          </a-tab-pane>
          <a-tab-pane :key="4" tab="模型指标监控">
            <div class="carousel">
              <div class="carousel-item">建议监控时长不低于 1 分钟，否则可能无法获取数据</div>
            </div>
            <ModelTarget :active-key="state.activeKey"></ModelTarget>
          </a-tab-pane>
        </template>
      </template>
    </a-tabs>
  </div>
  <a-drawer v-model:open="deployState.visible" width="800" :mask-closable="false">
    <template #title>
      部署
      <a-tooltip>
        <template #title>使用预置开源的模型并将其部署为在线服务，以进行实时的推理调用。</template>
        <QuestionCircleOutlined class="ml-5px" />
      </a-tooltip>
    </template>
    <Deploy :model="modelInfo" :close-visible="() => (deployState.visible = false)" />
  </a-drawer>
</template>

<style lang="less" scoped>
  .task-info {
    display: flex;
    flex-direction: column;
    height: 100%;

    > div:nth-child(1) {
      margin-bottom: 20px;
      font-size: 16px;
      font-weight: 600;
    }

    .particulars {
      height: 100%;

      > div:nth-child(2n) {
        margin-bottom: 40px;
      }

      .hyperparameter {
        max-height: 1000px;
        overflow: hidden;
        transition: max-height, 0.5s;
      }

      .fewer {
        max-height: 0 !important;
        transition: max-height, 0.5s;
      }

      .details {
        display: flex;
        cursor: pointer;

        > span {
          margin-right: 10px;
        }
      }
    }

    // overflow-scroll

    .textbefo {
      position: relative;
      padding: 5px 0 5px 20px;
      // padding-left: 40px;
      margin: 10px 0 20px;
      font-size: 15px;
      font-weight: 600;
    }

    .textbefo::before {
      position: absolute;
      top: 9px;
      left: 0;
      display: inline-block;
      width: 5px;
      height: 16px;
      content: '';
      background: rgb(38 61 252 / 82%);
    }
  }

  :deep(.ant-descriptions-bordered) {
    width: 100% !important;
  }

  :deep(.ant-collapse) {
    width: 100%;

    .ant-collapse-header {
      padding: 0 16px !important;
    }
  }

  // :deep(.ant-tabs-tab) {
  //   margin: 0 !important;
  //   padding: 8px 16px;
  //   border: 1px solid;

  //   +.ant-tabs-tab {
  //     border-left: none;
  //   }
  // }

  // TAP 栏样式
  :deep(.ant-tabs-top) {
    flex: 1;
    height: 0;
  }

  :deep(.ant-tabs-content) {
    height: 100%;
  }
</style>
