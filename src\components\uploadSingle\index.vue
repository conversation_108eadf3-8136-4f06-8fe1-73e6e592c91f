<!--
 * @Author: fz
 * @LastEditors: fz
 * @description: 单个上传
 * @Date: 2025-5-23 18:11:29
 * @LastEditTime: 2025-5-23 18:11:29
-->
<template>
  <a-upload-dragger
    v-model:file-list="fileList"
    :max-count="1"
    :accept="accept"
    :custom-request="customRequest"
    :before-upload="beforeUpload"
    :show-upload-list="false"
  >
    <p class="ant-upload-drag-icon">+</p>
    <p class="ant-upload-text">{{ text1 }}</p>
    <p class="ant-upload-hint">{{ text2 }}</p>
  </a-upload-dragger>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import { message, Upload } from 'ant-design-vue';
  import type { UploadProps } from 'ant-design-vue';
  import { uploadFile, uploadImg } from '@/api/classRoom';
  // import { UploadOutlined } from '@ant-design/icons-vue';
  import { getLocalItem } from '@/utils/common';
  const loading = ref(false);
  const props = withDefaults(
    defineProps<{
      fileSize?: number;
      accept?: any;
      text1?: string;
      text2?: string;
    }>(),
    {
      fileSize: 10, //文件大小
      accept: ['image/jpeg', 'image/jpg', 'image/png'], //上传的文件类型
      text1: '点击或拖拽上传课件',
      text2: '支持pptx、pdf、word文件',
    },
  );
  const emit = defineEmits(['getUploadData']);
  const fileList = ref<any>([]);
  const userInfo = JSON.parse(getLocalItem('HQSK_AI_PLATFORM_FRONTEND_USERINFO') || '{}');
  //限制上传类型
  const beforeUpload: UploadProps['beforeUpload'] = (file) => {
    const isAllowedType = props.accept.includes(file.type);
    fileList.value = [...fileList.value, file];
    const iconSize = file.size / 1024 / 1024 < props.fileSize;
    if (!isAllowedType) {
      if (file.type.includes('image')) {
        message.error('只能上传jpg、jpeg、png!');
      } else {
        message.error('只能上传pptx、pdf、word文件!');
      }
      return Upload.LIST_IGNORE;
    }
    if (!iconSize) {
      fileList.value.splice(fileList.value.indexOf(file), 1);
      message.error(`大小不能超过${props.fileSize}MB!`);
    }
    if (fileList.value.length > 0) {
      fileList.value = [file];
    }
    return iconSize || Upload.LIST_IGNORE;
  };
  const customRequest = async (options: any) => {
    loading.value = true;
    const { file } = options;
    let formData = new FormData();
    let res;
    if (file.type.includes('image')) {
      formData.append('images', file);
      formData.append('user_id', userInfo?.userId);
      res = await uploadImg(formData, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      });
    } else {
      formData.append('file', file);
      res = await uploadFile(formData, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      });
    }
    if (res) {
      emit('getUploadData', res);
      // message.success('上传成功');
    } else {
      loading.value = false;
      message.error('上传失败');
    }
  };
</script>
<style lang="less" scoped>
  .ant-upload-wrapper .ant-upload-drag {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    flex-wrap: nowrap;
    height: 100%; // 保证高度撑满
    min-height: 120px; // 可根据实际需要设置最小高度

    .ant-upload-drag-icon {
      margin-bottom: 0px;
      font-size: 24px;
      color: #969799;
      font-weight: 600;
    }
    .ant-upload-text {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #636466;
      line-height: 17px;
      font-style: normal;
    }
    .ant-upload-hint {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 10px;
      color: #636466;
      line-height: 14px;
      font-style: normal;
    }
  }
</style>
