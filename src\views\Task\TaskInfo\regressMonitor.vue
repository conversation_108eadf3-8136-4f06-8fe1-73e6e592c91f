<script setup lang="ts">
  import { getSklearnModelMonitor } from '@/api/model';
  import type { EChartsType } from 'echarts';
  import { init } from 'echarts';
  import { reactive, ref, watch, onMounted } from 'vue';
  import { debounce } from 'lodash-es';
  import { useRoute } from 'vue-router';
  import type { ColumnType } from 'ant-design-vue/es/table';

  const props = defineProps<{ activeKey: number }>();
  const route = useRoute();
  const dataSource = ref([]);
  const columns = ref<ColumnType[]>([]);
  const echarts = ref();
  const chartInstances: EChartsType[] = reactive([]);

  const gridItems = {
    left: '3%',
    right: '4%',
    bottom: '3%',
    top: '10%',
    containLabel: true,
  };
  const fetchData = async () => {
    const data = await getSklearnModelMonitor(String(route.query.taskid));
    const { prediction_ground_comparison, metrics } = data;
    const res: ColumnType[] = [];
    if (metrics) {
      Object.keys(metrics).forEach((key) => {
        res.push({ title: key, dataIndex: key });
      });
      columns.value = res;
      // @ts-expect-error
      dataSource.value = [metrics];
    }
    if (prediction_ground_comparison) {
      const { ground_truth, predict } = prediction_ground_comparison;
      const res: number[][] = [];
      ground_truth.forEach((num: number, index: number) => {
        const temp: number[] = [];
        temp.push(num);
        temp.push(predict[index]);
        res.push(temp);
      });
      renderEcharts(res);
    }
  };

  const renderEcharts = (data: number[][]) => {
    const echartInstance = init(echarts.value);
    chartInstances.push(echartInstance);
    echartInstance.setOption({
      grid: gridItems,
      xAxis: {},
      yAxis: {},
      tooltip: {
        position: 'top',
      },
      series: [
        {
          symbolSize: 20,
          data: data,
          type: 'scatter',
        },
      ],
    });
    echartInstance.resize();
  };
  watch(
    () => props.activeKey,
    (key) => {
      if (key !== 4) {
      } else {
        fetchData();
      }
    },
    { deep: true, immediate: true },
  );
  const resizeObserver = new ResizeObserver(
    // @ts-expect-error
    debounce((entries) => {
      for (const _ of entries) {
        console.log(_);
        chartInstances.forEach((item: EChartsType) => {
          item.resize();
        });
      }
    }, 300),
  );

  onMounted(() => {
    resizeObserver.observe(document.body);
  });
</script>

<template>
  <a-table :data-source="dataSource" :columns="columns" :pagination="false"> </a-table>
  <div ref="echarts" class="echart-box">
    <a-empty />
  </div>
</template>

<style scoped lang="less">
  .echart-box {
    height: calc(100% - 160px);
    margin-top: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #ccc;
  }
</style>
