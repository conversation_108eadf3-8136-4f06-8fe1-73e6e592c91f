<script setup lang="ts">
  import { ref, reactive, watch, nextTick } from 'vue';
  import { VAceEditor } from 'vue3-ace-editor';
  import 'ace-builds/src-noconflict/theme-chrome';
  import 'ace-builds/src-noconflict/theme-monokai.js';
  import 'ace-builds/src-noconflict/mode-python.js';
  import 'ace-builds/src-noconflict/mode-sh.js';
  import Debugging from './Debugging.vue';
  import MonitorOperation from './MonitorOperation.vue';
  import { useRoute, useRouter } from 'vue-router';
  import { fetchDeployInfo, fetchDeployLogs } from '@/api/deploy';
  import { DeployStatus, deployMethod } from '@/views/Deploy/index';
  import type { IFetchDeployDetail } from '@/interface/deploy';
  import type { IAceOptions } from '@/interface';
  import { convertIsoTimeToLocalTime } from '@/utils/common';
  import { GPUList } from '@/views/Model/Train/index';
  import { DEPLOY_STATE_MAP } from '../index';
  import { machineLearningList } from '../../Model/Manage/index';
  const route = useRoute();
  const router = useRouter();

  const aceEditor = ref();

  interface IState {
    deployInfo: IFetchDeployDetail | null;
    activeKey: string | number;
    basicInfo: { name: string; val: string }[];
    spinning: boolean;
    logStr: string;
    editor: null;
  }
  const DETAULT_COLUMNS = [
    { name: '服务名/服务ID', val: 'service_name' },
    { name: '部署模型名', val: 'model_name' },
    { name: '部署方式', val: 'deploy_method' },
    { name: '状态', val: 'status' },
    { name: '创建时间', val: 'created_at' },
    { name: '更新时间', val: 'updated_at' },
    { name: '显卡信息', val: 'gpu_number_list' },
    { name: '服务简介', val: 'introduction' },
  ];
  const runCommand = ref(``);
  const state = reactive<IState>({
    deployInfo: null,
    activeKey: 1,
    basicInfo: JSON.parse(JSON.stringify(DETAULT_COLUMNS)),
    spinning: false,
    logStr: '',
    editor: null,
  });

  const aceOptions: IAceOptions = {
    enableBasicAutocompletion: true,
    enableSnippets: true,
    enableLiveAutocompletion: true,
    tabSize: 4,
    enableEmmet: true,
    fontSize: 15,
    showPrintMargin: false, // 去除编辑器里的竖线
    highlightActiveLine: true,
    useWorker: true,
    readOnly: true,
  };

  // @ts-expect-error
  const editorInit = (editor) => (state.editor = editor);

  // const editorInit = (editor: any) => ({
  //   state.editor = editor
  //   editor.getSession().on('changeScrollTop', async (e: any) => {
  //     let bool = true
  //     if (e == -0 && bool) { }
  //   })
  // })

  const keyMap = ['introduce', 'log', 'monitor', 'debuger'];
  const activeKey = ref(route.query.tab && keyMap.includes(route.query.tab as string) ? route.query.tab : 'introduce');
  const handleChange = (key: string) => {
    router.push({
      path: route.path,
      query: { ...route.query, tab: key },
    });
  };

  watch(
    () => activeKey.value,
    async (key) => {
      if (key !== undefined) {
        state.spinning = true;
        state.deployInfo = await fetchDeployInfo(route.query.id as string);
        if (key == 'introduce') {
          if (state.deployInfo && machineLearningList.includes(state.deployInfo.model_category)) {
            state.basicInfo = [
              ...DETAULT_COLUMNS,
              ...[
                { name: '数据集名称', val: 'dataset_parameter_name' },
                { name: '数据集类型', val: 'dataset_parameter_type' },
                { name: '数据特征', val: 'dataset_parameter_numeric_features' },
                { name: '数据指标', val: 'dataset_parameter_numeric_labels' },
              ],
            ];
          }
          runCommand.value = '';
          if (state.deployInfo && state.deployInfo.run_command) {
            state.deployInfo.run_command.forEach((str) => {
              runCommand.value += `${str}
`;
            });
          }
        }
        if (key == 'log') {
          state.spinning = true;
          state.logStr = await fetchDeployLogs(route.query.id as string, '500');
          nextTick(() => {
            if (state.editor) {
              // @ts-expect-error
              const session = state.editor.getSession();
              const lastLine = session.getLength(); // 获取最后一行
              // @ts-expect-error
              state.editor.gotoLine(lastLine); // 将光标移动到最后一行
              // @ts-expect-error
              state.editor.scrollToLine(lastLine, true, true, () => {}); // 滚动到最后一行
            }
          });
          state.spinning = false;
        }
        state.spinning = false;
      }
    },
    { deep: true, immediate: true },
  );

  // @ts-expect-error
  const jump = (obj) => {
    const { val } = obj;
    if (state.deployInfo?.model_id && val == 'model_name') {
      // 基本信息 模型跳转
      router.push({
        path: '/model/detail',
        query: { ...route.query, modelid: state.deployInfo.model_id },
      });
    }
  };
</script>

<template>
  <div class="task-info">
    <div>{{ state.deployInfo?.service_name || '-------' }}</div>
    <a-tabs v-model:active-key="activeKey" @change="handleChange">
      <a-tab-pane key="introduce" tab="服务介绍">
        <div class="particulars overflow-scroll">
          <a-spin :spinning="state.spinning">
            <div class="textbefo">基本信息</div>
            <a-descriptions size="small" :column="2">
              <a-descriptions-item v-for="(d, l) in state.basicInfo" :key="l" :label="d?.name || '---'">
                <div
                  :style="
                    d?.val == 'model_name' && state.deployInfo?.model_id ? { color: '#1677ff', cursor: 'pointer' } : {}
                  "
                  @click="jump(d)"
                >
                  <a-tag
                    v-if="d?.val == 'status'"
                    :color="DeployStatus.find((item) => item.value === state.deployInfo?.[d?.val])?.color"
                    >{{ DeployStatus.find((item) => item.value === state.deployInfo?.[d?.val])?.label }}
                  </a-tag>
                  <div v-else-if="['created_at', 'updated_at'].includes(d?.val)">
                    {{
                      state.deployInfo?.[d?.val] ? convertIsoTimeToLocalTime(String(state.deployInfo?.[d?.val])) : '--'
                    }}
                  </div>
                  <div v-else-if="d?.val === 'dataset_parameter_name'">
                    {{ state.deployInfo?.dataset_parameter.name }}
                  </div>
                  <div v-else-if="d?.val === 'dataset_parameter_type'">
                    {{ state.deployInfo?.dataset_parameter.type }}
                  </div>
                  <div v-else-if="d?.val === 'dataset_parameter_numeric_features'">
                    {{ state.deployInfo?.dataset_parameter.numeric_features!.join('、') }}
                  </div>
                  <div v-else-if="d?.val === 'dataset_parameter_numeric_labels'">
                    {{ state.deployInfo?.dataset_parameter.numeric_labels!.join('、') }}
                  </div>
                  <div v-else-if="d?.val === 'gpu_number_list'">
                    {{
                      state.deployInfo?.[d?.val]
                        ? // @ts-expect-error
                          GPUList.filter((item) => state.deployInfo?.[d?.val].includes(item.value))
                            .map((i) => i.label)
                            .join(',')
                        : '--'
                    }}
                  </div>
                  <div v-else>
                    {{
                      d?.val == 'deploy_method'
                        ? deployMethod.find((item) => item.value === state.deployInfo?.[d?.val])?.label
                        : state.deployInfo?.[d?.val] || '--'
                    }}
                  </div>
                </div>
              </a-descriptions-item>
              <a-descriptions-item />
            </a-descriptions>
            <div class="textbefo">计算资源配置</div>
            <a-descriptions size="small" :column="2">
              <a-descriptions-item label="计算资源">{{ '公共资源' }}</a-descriptions-item>
              <a-descriptions-item>
                <a-descriptions size="small" :column="1" layout="vertical">
                  <a-descriptions-item label="资源列表">
                    <a-table
                      :columns="[
                        { title: 'GPU', dataIndex: 'gpu' },
                        { title: 'CPU', dataIndex: 'cpu' },
                        { title: '内存', dataIndex: 'storage' },
                      ]"
                      :data-source="[{ key: '1', gpu: '1 * NVIDIA V100', cpu: 'Dynamic', storage: 'Dynamic' }]"
                      bordered
                      :pagination="false"
                    >
                    </a-table>
                  </a-descriptions-item>
                </a-descriptions>
              </a-descriptions-item>
            </a-descriptions>
            <div class="textbefo">镜像</div>
            <a-descriptions size="small" :column="2">
              <a-descriptions-item label="镜像">{{
                state.deployInfo ? state.deployInfo.docker_image : '--'
              }}</a-descriptions-item>
            </a-descriptions>

            <div class="textbefo">初始命令</div>
            <v-ace-editor
              v-model:value="runCommand"
              lang="sh"
              theme="monokai"
              :options="aceOptions"
              style="height: 300px; border: 1px solid #dbd3d3"
            />
          </a-spin>
        </div>
      </a-tab-pane>

      <a-tab-pane key="log" :disabled="[''].includes(state.deployInfo?.status as string)" tab="服务日志">
        <a-spin :spinning="state.spinning" class="log-spin">
          <v-ace-editor
            ref="aceEditor"
            v-model:value="state.logStr"
            lang="sh"
            theme="monokai"
            :options="aceOptions"
            style="height: 100%; border: 1px solid #dbd3d3"
            @init="editorInit"
          />
        </a-spin>
      </a-tab-pane>

      <template
        v-if="
          !['blender', 'p2l'].includes(state.deployInfo?.model_category!) &&
          state.deployInfo?.status !== DEPLOY_STATE_MAP.FAILED
        "
      >
        <a-tab-pane key="monitor" :disabled="['exited'].includes(state.deployInfo?.status as string)" tab="服务监控">
          <MonitorOperation :created="state.deployInfo?.created_at"></MonitorOperation>
        </a-tab-pane>
        <a-tab-pane
          key="debuger"
          :disabled="['exited', 'stopped', 'creating'].includes(state.deployInfo?.status as string)"
          tab="服务调试"
        >
          <Debugging
            :source_category="state.deployInfo?.model_category"
            :dataset_parameter="state.deployInfo?.dataset_parameter"
          />
        </a-tab-pane>
      </template>
    </a-tabs>
  </div>
</template>

<style scoped lang="less">
  .task-info {
    display: flex;
    flex-direction: column;
    height: 100%;

    > div:nth-child(1) {
      margin-bottom: 20px;
      font-size: 16px;
      font-weight: 600;
    }

    .particulars {
      height: 100%;

      > div:nth-child(2n) {
        margin-bottom: 40px;
      }

      .hyperparameter {
        max-height: 1000px;
        overflow: hidden;
        transition: max-height, 0.5s;
      }

      .fewer {
        max-height: 0 !important;
        transition: max-height, 0.5s;
      }

      .details {
        display: flex;
        cursor: pointer;

        > span {
          margin-right: 10px;
        }
      }
    }

    // overflow-scroll
  }

  :deep(.ant-descriptions-bordered) {
    width: 100% !important;
  }

  // log spin
  :deep(.ant-tabs-tabpane > .ant-spin-nested-loading) {
    height: 100%;

    .ant-spin-container {
      height: 100%;
    }
  }

  :deep(.ant-collapse) {
    width: 100%;

    .ant-collapse-header {
      padding: 0 16px !important;
    }
  }

  // :deep(.ant-tabs-tab) {
  //   margin: 0 !important;
  //   padding: 8px 16px;
  //   border: 1px solid;

  //   +.ant-tabs-tab {
  //     border-left: none;
  //   }
  // }

  // TAP 栏样式
  :deep(.ant-tabs-top) {
    flex: 1;
    height: 0;
  }

  :deep(.ant-tabs-content) {
    height: 100%;
  }
</style>
