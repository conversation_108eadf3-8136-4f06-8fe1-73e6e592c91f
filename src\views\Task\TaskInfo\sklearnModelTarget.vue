<script setup lang="ts">
  import { getSklearnModelMonitor } from '@/api/model';
  import type { EChartsType } from 'echarts';
  import { init } from 'echarts';
  import { reactive, ref, watch, onMounted } from 'vue';
  import { debounce } from 'lodash-es';
  import { useRoute } from 'vue-router';

  const props = defineProps<{ activeKey: number }>();
  const route = useRoute();
  const echarts = ref();
  const metricsEcharts = ref();
  const chartInstances: EChartsType[] = reactive([]);
  const dataSource = reactive({
    roc: {},
    metrics: {
      accuracy: 0,
      precision: 0,
      recall: 0,
      f1_score: 0,
    },
  });
  const classes = ref<string[]>([]);
  const activeClass = ref('');
  const currentClass = reactive({
    fpr: [],
    tpr: [],
  });
  const gridItems = {
    left: '3%',
    right: '4%',
    bottom: '3%',
    top: '10%',
    containLabel: true,
  };

  const renderMetricsEcharts = () => {
    const echartInstance = init(metricsEcharts.value);
    chartInstances.push(echartInstance);
    const res = dataSource.metrics;
    echartInstance.setOption({
      xAxis: {
        type: 'category',
        data: ['准确率', 'F1分数', '精确率', '召回率'],
      },
      yAxis: {
        type: 'value',
      },
      grid: gridItems,
      tooltip: {
        show: true,
        trigger: 'axis',
      },
      series: [
        {
          data: [res.accuracy, res.f1_score, res.precision, res.recall],
          type: 'bar',
        },
      ],
    });
    echartInstance.resize();
  };
  const renderEcharts = (fpr: number[], tpr: number[], category: string) => {
    const echartInstance = init(echarts.value);
    chartInstances.push(echartInstance);
    const len = tpr.length;
    const step = 1 / len;
    const array = Array.from({ length: len }, (_, i) => i * step);
    echartInstance.setOption({
      legend: {
        data: [category, 'Random cClassifier'],
        top: '10',
      },
      grid: gridItems,
      xAxis: {
        type: 'category',
        nameLocation: 'middle',
        nameGap: 30,
        data: fpr,
      },
      yAxis: {
        type: 'value',
      },
      tooltip: {
        show: true,
        trigger: 'axis',
      },
      series: [
        {
          data: tpr,
          type: 'line',
          name: category,
        },
        {
          data: array,
          type: 'line',
          name: 'Random cClassifier',
        },
      ],
    });
    echartInstance.resize();
  };

  const fetchData = async () => {
    const data = await getSklearnModelMonitor(String(route.query.taskid));
    const { roc } = data;
    const { ROC, metrics } = roc;
    Object.assign(dataSource, { roc: ROC, metrics });
    classes.value = Object.keys(roc.ROC || {});
    if (classes.value.length) {
      activeClass.value = classes.value[0];
    }
    renderMetricsEcharts();
  };
  watch(
    () => activeClass.value,
    () => {
      if (activeClass.value) {
        // @ts-expect-error
        const res = dataSource.roc[activeClass.value];
        const { fpr, tpr } = res;
        Object.assign(currentClass, { fpr, tpr });

        renderEcharts(currentClass.fpr, currentClass.tpr, activeClass.value);
      }
    },
    { deep: true, immediate: true },
  );
  watch(
    () => props.activeKey,
    (key) => {
      if (key !== 4) {
      } else {
        fetchData();
      }
    },
    { deep: true, immediate: true },
  );

  const resizeObserver = new ResizeObserver(
    // @ts-expect-error
    debounce((entries) => {
      for (const _ of entries) {
        console.log(_);
        chartInstances.forEach((item: EChartsType) => {
          item.resize();
        });
      }
    }, 300),
  );

  onMounted(() => {
    resizeObserver.observe(document.body);
  });
</script>

<template>
  <div class="echart flex justify-between">
    <div class="echarts-container mt-32px">
      <div ref="metricsEcharts" class="echart-box">
        <a-empty />
      </div>
    </div>
    <div class="echarts-container">
      <a-radio-group v-model:value="activeClass">
        <a-radio-button v-for="item in classes" :key="item" :value="item">{{ item }}</a-radio-button>
      </a-radio-group>
      <div ref="echarts" class="echart-box">
        <a-empty />
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
  .echart {
    height: calc(100% - 10px);
  }
  .echarts-container {
    width: calc(50% - 10px);
  }
  .echart-box {
    height: calc(100% - 72px);
    margin-top: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #ccc;
    &:first-child {
      height: calc(100% - 40px);
    }
  }
</style>
