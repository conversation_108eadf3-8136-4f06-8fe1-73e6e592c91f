<script setup lang="ts">
  import { ref, reactive, watch } from 'vue';
  import {
    CopyOutlined,
    PlusOutlined,
    MinusCircleOutlined,
    FullscreenOutlined,
    FullscreenExitOutlined,
  } from '@ant-design/icons-vue';
  import { copyText } from '@/utils/common';
  import { VAceEditor } from 'vue3-ace-editor';
  import 'ace-builds/src-noconflict/theme-chrome';
  import 'ace-builds/src-noconflict/theme-monokai.js';
  import 'ace-builds/src-noconflict/mode-python.js';
  import 'ace-builds/src-noconflict/mode-sh.js';
  import { deployCall, deployOCRCall, deploySklearnCall } from '@/api/deploy';
  import { useRoute } from 'vue-router';
  import type { IBodyProps, IDeployCallProps } from '@/interface/deploy';
  import { message } from 'ant-design-vue';
  import type { IAceOptions } from '@/interface';
  import { machineLearningList } from '../../Model/Manage/index';
  import type { IDatasetParameter } from '@/interface/model';
  interface IProps {
    source_category: string;
    dataset_parameter?: IDatasetParameter;
  }
  const props = withDefaults(defineProps<IProps>(), {
    source_category: 'llm',
  });
  const route = useRoute();
  const api = ref(`${location.origin}/data/ai-platform-backend/api/v1/deploy/api/call/${route.query.id}`);
  const activeKey = ref('body');
  const loading = ref(false);
  interface IState {
    langEnum: Array<'SQL' | 'Python' | 'sh'>;
    isFull: boolean;
    body: IBodyProps | { url: string };
    header: {
      index: number;
      value: string;
      label: string;
    }[];
    response: string;
    editor: unknown;
  }
  const state: IState = reactive({
    langEnum: ['SQL', 'Python', 'sh'],
    isFull: false,
    body: {
      messages: [
        {
          role: 'user',
          content: '你好',
        },
      ],
      max_tokens: 100,
    },
    header: [],
    response: '暂无数据',
    editor: null,
  });
  const columns = [
    { title: 'key', dataIndex: 'label', key: 'label', scopedSlots: { customRender: 'label' } },
    { title: 'value', dataIndex: 'value', key: 'value', scopedSlots: { customRender: 'value' } },
    { title: '操作', dataIndex: 'operation', key: 'operation', scopedSlots: { customRender: 'operation' }, width: 100 },
  ];
  const aceOptions: IAceOptions = {
    enableBasicAutocompletion: true,
    enableSnippets: true,
    enableLiveAutocompletion: true,
    tabSize: 4,
    enableEmmet: true,
    fontSize: 15,
    showPrintMargin: false, // 去除编辑器里的竖线
    highlightActiveLine: true,
    useWorker: true,
    readOnly: true,
    wrap: true,
  };

  // @ts-expect-error
  const editorInit = (editor) => (state.editor = editor);

  const addRow = () => {
    const newRow = {
      index: state.header.length + 1,
      value: '',
      label: '',
    };
    state.header.push(newRow);
  };
  const deleteRow = (index: number) => {
    state.header = state.header.filter((item) => item.index !== index);
  };

  const handleCall = async () => {
    if (!state.body || Object.keys(state.body).length === 0) {
      message.warn('body 参数必填');
      return;
    }
    loading.value = true;
    const headers: Record<string, string> = {};
    state.header.forEach((item) => {
      headers[item.label] = item.value;
    });
    let params:
      | IDeployCallProps
      | {
          svc_id: string;
          url: string;
        };
    if (['llm', 'multimodal'].includes(props.source_category)) {
      params = {
        model_id: route.query.id as string,
        body: {
          model: 'Qwen2-0.5B-Instruct',
          ...state.body,
        } as IBodyProps,
        headers,
      };
    } else if (machineLearningList.includes(props.source_category)) {
      const { numeric_features } = props.dataset_parameter!;
      const res: Record<string, string | number> = {};
      if (numeric_features) {
        numeric_features.forEach((key) => (res[key] = 0));
      }
      params = {
        svc_id: route.query.id as string,
        //@ts-expect-error
        payload: res,
      };
    } else {
      params = {
        svc_id: route.query.id as string,
        url: (state.body as { url: string }).url || '',
      };
    }

    try {
      // deploySklearnCall
      let res = '';
      if (['llm', 'multimodal'].includes(props.source_category)) {
        res = await deployCall(params as IDeployCallProps);
      } else if (machineLearningList.includes(props.source_category)) {
        //@ts-expect-error
        res = await deploySklearnCall(params);
      } else {
        res = await deployOCRCall(
          params as {
            svc_id: string;
            url: string;
          },
        );
      }
      // const res = ['llm', 'multimodal'].includes(props.source_category)
      //   ? await deployCall(params as IDeployCallProps)
      //   : await deployOCRCall(
      //       params as {
      //         svc_id: string;
      //         url: string;
      //       },
      //     );
      state.response = JSON.stringify(res, null, 4);
    } catch {}
    loading.value = false;
  };
  watch(
    () => props.source_category,
    (source_category) => {
      if (source_category) {
        if (['gotocr', 'ocr', 'paddleocr'].includes(source_category)) {
          state.body = { url: 'https://minio-prod.shukeyun.com/deepfox/da674742b81f8142d8ddb9ef2c9b37a3.jpg' };
        }
        if (source_category === 'multimodal') {
          state.body = {
            messages: [
              {
                role: 'user',
                // @ts-expect-error
                content: [
                  {
                    type: 'image_url',
                    image_url: { url: 'https://qianwen-res.oss-cn-beijing.aliyuncs.com/Qwen-VL/assets/demo.jpeg' },
                  },
                  { type: 'text', text: 'Describe this image.' },
                ],
              },
            ],
          };
        }
        if (machineLearningList.includes(source_category)) {
          const { numeric_features } = props.dataset_parameter!;
          const res: Record<string, string | number> = {};
          if (numeric_features) {
            numeric_features.forEach((key) => (res[key] = 0));
          }
          // @ts-expect-error
          state.body = res;
        }
      }
    },
    { deep: true, immediate: true },
  );
</script>

<template>
  <div class="particulars overflow-scroll p-x-10px">
    <div class="left">
      <div class="textbefo">请求信息</div>
      <a-input v-model:value="api">
        <template #addonBefore> POST </template>
        <template #addonAfter>
          <CopyOutlined @click="copyText(api)" />
        </template>
      </a-input>
      <div class="mt-20px flex w-100% justify-between items-center">
        <a-radio-group v-model:value="activeKey" :style="{ marginBottom: '8px' }">
          <a-radio-button value="body">Body</a-radio-button>
          <a-radio-button value="header">Headers</a-radio-button>
        </a-radio-group>
        <a-button type="primary" :loading="loading" @click="handleCall">发送</a-button>
      </div>
      <div v-if="activeKey === 'body'">
        <json-editor-vue v-model="state.body" class="h-485px" language="zh_CN" />
      </div>
      <div v-else>
        <div class="flex justify-end w-100% m-y-10px">
          <a-button @click="addRow">
            <template #icon>
              <PlusOutlined />
            </template>
          </a-button>
        </div>
        <a-table :data-source="state.header" bordered :columns="columns" :pagination="false" :scroll="{ y: 380 }">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'label'">
              <a-input v-model:value="record.label" />
            </template>
            <template v-if="column.dataIndex === 'value'">
              <a-input v-model:value="record.value" />
            </template>
            <template v-if="column.dataIndex === 'operation'">
              <MinusCircleOutlined :style="{ fontSize: '20px', color: '#ff4d4f' }" @click="deleteRow(record.index)" />
            </template>
          </template>
        </a-table>
      </div>
    </div>

    <div class="right">
      <div class="textbefo">返回信息</div>
      <div :class="{ 'full-screen': state.isFull }">
        <a-spin :spinning="loading">
          <div class="full">
            <FullscreenExitOutlined v-if="state.isFull" @click="state.isFull = false" />
            <FullscreenOutlined v-else @click="state.isFull = true" />
          </div>
          <v-ace-editor
            v-if="state.isFull"
            ref="aceEditor"
            v-model:value="state.response"
            :lang="state.langEnum[2].toLowerCase()"
            theme="monokai"
            :options="aceOptions"
            :style="{ height: 'calc(100vh - 30px)' }"
            @init="editorInit"
          />
          <v-ace-editor
            v-else
            ref="aceEditor"
            v-model:value="state.response"
            @init="editorInit"
            :lang="state.langEnum[2].toLowerCase()"
            theme="monokai"
            :options="aceOptions"
            :style="{ height: '550px', border: '1px solid #dbd3d3' }"
          />
        </a-spin>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
  .particulars {
    display: flex;
    justify-content: space-between;

    > div {
      width: 49%;
    }

    .full {
      display: flex;
      justify-content: end;
      height: 30px;
      padding-right: 15px;
      background: #f5f5f5;

      span {
        cursor: pointer;
      }
    }

    .full-screen {
      position: fixed;
      top: 0;
      right: 0;
      bottom: 0;
      z-index: 1000;
      width: 100vw;
      height: 100vh;
    }
  }

  :deep(.jsoneditor) {
    border-color: #ccc;
  }

  :deep(.jsoneditor-menu) {
    background-color: #ccc;
    border-color: #ccc;
  }
</style>
