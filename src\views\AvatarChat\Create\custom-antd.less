.custom-spin {
  display: flex;
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
  // height: 100vh; /* 设置父容器高度 */
}


.loading-spin{
  .ant-spin-text {
    margin-top: 16px;
    width: 144px;
    height: 24px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: #969799;
    line-height: 24px;
    text-align: right;
    font-style: normal;
  }
}


.custom-tooltip {
  cursor: default;

  .ant-tooltip-inner {
    width: 250px;
    padding: 8px;
    background: rgb(23 24 26 / 75%);
    border-radius: 4px;
  }

  .tip-item {
    font-size: 10px;
    font-weight: 400;
    line-height: 14px;
    color: #fff;
  }
}

.custom-radio-group {
  display: flex;
  gap: 16px;
  font-family: PingFangSC, 'PingFang SC', sans-serif;
  font-size: 12px;
  font-weight: 500;
  line-height: 17px;
  color: #17181a;

  .radio-item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 175px;
    height: 58px;
    padding: 10px;
    pointer-events: auto;
    cursor: default;
    background: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    transition: all 0.3s;

    &.disabled {
      pointer-events: none;

      .desc {
        color: rgb(0 0 0 / 25%);
      }
    }

    &:hover,
    &.selected {
      border: 1px solid #1677ff;
    }

    &.selected {
      background: #e6f4ff;
      border-width: 2px;
    }

    .desc {
      margin-left: 20px;
      font-size: 10px;
      font-weight: 400;
      line-height: 14px;
      color: #636466;
    }
  }
}

.custom-upload-avatar {
  // padding-bottom: 20px;
  // border-bottom: 1px solid #f0f1f2;

  .avatar-uploader {
    &.error {
      .ant-upload.ant-upload-select,
      .ant-upload.ant-upload-select:hover {
        border: 1px dashed #ff3c16;

        .upload-plus-icon {
          color: #ff3c16;
        }
      }
    }

    .ant-upload.ant-upload-select {
      width: 200px;
      // width: 98px;
      height: 98px;
    }

    .upload-icon-box {
      display: flex;
      flex-direction: column;

      .upload-button {
        background: #f7f8fa;
        border: 0;

        .upload-tip {
          height: 17px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #636466;
          line-height: 17px;
          text-align: center;
          font-style: normal;
          margin-bottom: 0;
        }

        .upload-format-tip {
          display: flex;
          justify-content: flex-start;
          flex-direction: column;
          height: 14px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 10px;
          color: #969799;
          line-height: 14px;
          text-align: center;
          font-style: normal;
          margin-bottom: 0;
        }
      }
    }

    .upload-preview {
      position: relative;
      width: 200px;
      height: 100%;
      border-radius: 8px;

      &:hover > .upload-overlay {
        opacity: 1;
      }

      .avatar-icon {
        max-width: 198px;
        width: auto;
        height: 96px;
        background: #fff;
        border-radius: 4px;
      }

      .upload-overlay {
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        font-size: 12px;
        font-weight: 400;
        line-height: 17px;
        color: white;
        background: rgb(0 0 0 / 50%);
        opacity: 0;
        transition: opacity 0.3s;
        border-radius: 8px;

        .re-upload-icon {
          width: 13px;
          height: 12px;
          margin-right: 6px;
        }
      }
    }
  }

  .video-uploader {
    &.error {
      .ant-upload.ant-upload-select,
      .ant-upload.ant-upload-select:hover {
        border: 1px dashed #ff3c16;

        .upload-plus-icon {
          color: #ff3c16;
        }
      }
    }

    .ant-upload.ant-upload-select,
    .ant-upload.ant-upload-select-picture-card {
      width: 100%;
      max-width: 484px;
      height: auto;
      aspect-ratio: 16 / 9;
      padding: 0;
      overflow: hidden;
    }

    .upload-icon-box {
      display: flex;
      flex-direction: column;
      width: 350px;

      .upload-button {
        background: #f7f8fa;
        border: 0;

        .upload-tip {
          height: 17px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #636466;
          line-height: 17px;
          text-align: center;
          font-style: normal;
          margin-bottom: 8px;
        }

        .upload-format-tip {
          display: flex;
          justify-content: flex-start;
          flex-direction: column;
          height: 14px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          font-size: 10px;
          color: #969799;
          line-height: 14px;
          text-align: left;
          font-style: normal;
          margin-bottom: 0;
        }

        // 上传进度样式
        .upload-progress-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 40px 0;
          min-height: 200px;
          width: 100%;

          .video-upload-icon {
            margin-bottom: 28px;
            // opacity: 0.8;
          }

          .upload-progress{
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;

            
          

            .upload-progress-bar {
              width: 100%;
              max-width: 350px;
              margin-bottom: 20px;

              .progress-bg {
                width: 100%;
                height: 2px;
                background: #f0f1f2;
                border-radius: 4px;
                overflow: hidden;
                margin-bottom: 12px;

                .progress-fill {
                  height: 100%;
                  background: #1777ff;
                  border-radius: 4px;
                  transition: width 0.3s ease;
                }
              }

              .progress-info {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                width: 100%;

                .progress-text {
                  font-size: 12px;
                  color: #636466;
                  font-weight: 400;
                }

                .progress-percent {
                  margin-left: 2px;
                  font-size: 12px;
                  color: #636466;
                  font-weight: 500;
                }
              }
            }

            .cancel-upload-btn {
              margin-bottom: 16px;

              .ant-btn {
                padding: 4px 20px;
                font-size: 12px;
                color: #1777ff;
                // background: #fff;
                // border: 1px solid #1777ff;
                border-radius: 6px;
                font-weight: 400;
                height: auto;

                &:hover {
                  opacity: 0.9;
                  // background: #1777ff;
                  // color: #fff;
                  // border-color: #1777ff;
                }
              }
            }
          }
        }
      }
    }

    .upload-preview {
      position: relative;
      width: 100%;
      height: 100%;
      border-radius: 8px;
      overflow: hidden;

      &:hover > .upload-overlay {
        opacity: 1;
      }

      .avatar-icon {
        width: 100%;
        height: 100%;
        object-fit: cover; // use 'contain' if you prefer no cropping
        background: #fff;
        border-radius: 4px;
        display: block;
      }

      .upload-overlay {
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        font-size: 12px;
        font-weight: 400;
        line-height: 17px;
        color: white;
        background: rgb(0 0 0 / 50%);
        opacity: 0;
        transition: opacity 0.3s;
        border-radius: 8px;

        .re-upload-icon {
          width: 13px;
          height: 12px;
          margin-right: 6px;
        }
      }
    }
  }


  .avatar-format-tip {
    position: relative;
    top: -6px;
    height: 14px;
    font-size: 10px;
    font-weight: 400;
    line-height: 14px;
    color: #636466;
  }
}

.custom-speakers-section {
  padding-bottom: 20px;
  // border-bottom: 1px solid #f0f1f2;
}

.ant-input-affix-wrapper .ant-input {
  background-color: #ffffff;
}

.speakers-btn {
  // 基础态
  .ant-input {
    background-color: #f7f8fa;
    color: #17181a;
  }
  .ant-input-suffix {
    color: #969799;
  }
  border: none !important;
  box-shadow: none !important;

  // hover 态
  &:hover,
  &.ant-input-affix-wrapper:hover {
    border: none !important;
    box-shadow: none !important;
    .ant-input {
      color: #17181A;
      // background: #e6f4ff;
      &::placeholder {
        color: #17181A;
      }
    }
    .ant-input-suffix {
      color: #17181A;
    }
  }

  // 选中展示态（根据类 is-selected 控制）
  &.is-selected,
  &.is-selected:hover {
    border: none !important;
    box-shadow: none !important;
    .ant-input {
      color: #1677ff;
      // background: #e6f4ff;
    }
    .ant-input-suffix {
      color: #1677ff;
    }
  }

  // 选中后再次 hover，整体透明度 0.8
  &.is-selected:hover {
    opacity: 0.8;
  }

  // 聚焦态（Ant 会加 focus 样式，这里也去掉边框/阴影）
  &.ant-input-affix-wrapper-focused,
  &:focus,
  &:focus-within {
    border: none !important;
    box-shadow: none !important;
    .ant-input::placeholder {
      color: #17181A;
    }
  }
}

.ant-modal .ant-modal-content {
  box-shadow: none;
}

.ant-popover-content {
  .ant-popover-arrow {
    top: 22px;

  }
}

