<script setup lang="ts">
  import {
    featchDatasetOverview,
    featchDatasetItems,
    publishDataset,
    updateDatasetItems,
    deleteDataset,
    deleteDatasetItems,
    insertDatasetItems,
    exportDataset,
    updateDatasetField,
    delDatasetField,
  } from '@/api/dataset';
  import type { Fields, IDatasetOverview, exportFormatType } from '@/interface/dateset';
  import { ref, onMounted, reactive, computed } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { LeftOutlined } from '@ant-design/icons-vue';
  import { convertIsoTimeToLocalTime, getLocalItem, scientificToDecimal } from '@/utils/common';
  import { datasetTypes, publish_status, exportFormatOptions, DATASET_PUBLISH_STATE_MAP } from '.';
  import type { ColumnType, TablePaginationConfig } from 'ant-design-vue/es/table';
  import { InfoCircleOutlined } from '@ant-design/icons-vue';
  import type { IPage } from '@/interface';
  import { TABLE_PAGINATION } from '@/json/common';
  import { message } from 'ant-design-vue';
  import { administratorIds } from '@/utils/enum';
  import type { IUpdateDatasetField } from '@/interface/dateset';
  const route = useRoute();
  const router = useRouter();
  const { userId } = JSON.parse(getLocalItem('HQSK_AI_PLATFORM_FRONTEND_USERINFO') || '{}');
  const isAdministrator = computed(() => administratorIds.includes(userId));
  const activeKey = ref('data');
  const export_format = ref<exportFormatType>('json');
  const visible = ref(false);
  const delVisible = ref(false);
  const exportVisible = ref(false);
  const updateFieldVisible = ref(false);
  const isInsert = ref(false);
  const dataLoading = ref(false);
  const currentIndex = ref(-1);
  const formRef = ref();
  const fieldFormRef = ref();
  // @ts-expect-error
  const detail = reactive<IDatasetOverview>({});
  const dataOverview = ref([]);
  const fieldsOverview = ref<Fields[]>([]);
  const dataColumns = ref<ColumnType[]>([]);
  const fieldsColumns: ColumnType[] = [
    { title: '字段名', dataIndex: 'field_name' },
    { title: '字段类型', dataIndex: 'field_type' },
    { title: '字段限制', dataIndex: 'required' },
    { title: '字段备注', dataIndex: 'field_description' },
    { title: '操作', dataIndex: 'operation', width: '120px' },
  ];
  const data_pagination = reactive({ ...TABLE_PAGINATION });
  const data_page: IPage = reactive({ page: 1, limit: 10 });
  const fields_pagination = reactive({ ...TABLE_PAGINATION });
  const fields_page: IPage = reactive({ page: 1, limit: 10 });
  const currentDataItem = reactive<Record<string, string | number>>({});
  const currentDataField = reactive<IUpdateDatasetField>({
    field_name: '',
    field_type: '',
    prev_field_name: '',
    field_description: undefined,
    required: false,
  });

  const permission = computed(() =>
    route.query.source === 'all'
      ? detail.publish_status === DATASET_PUBLISH_STATE_MAP.PUBLISHED
      : isAdministrator.value
        ? detail.publish_status === DATASET_PUBLISH_STATE_MAP.PUBLISHED
        : true,
  );
  const resetDataItem = (data: Fields[]) => {
    const record: Record<string, string | number> = {};
    data.forEach((field) => {
      // @ts-expect-error
      record[field.field_name] = undefined;
    });
    Object.assign(currentDataItem, record);
  };
  const getFieldsList = () => {
    const { page, limit } = fields_page;
    fields_pagination.total = detail.fields.length;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const res = detail.fields.slice(startIndex, endIndex);
    fieldsOverview.value = res;
    // return data.slice(startIndex, endIndex);
  };
  const getOverview = async () => {
    const data: IDatasetOverview = await featchDatasetOverview(String(route.params.id));
    Object.assign(detail, data);
    if (data.fields) {
      resetDataItem(data.fields);
      getFieldsList();
    }
  };
  const getItems = async () => {
    dataLoading.value = true;
    const data = await featchDatasetItems(String(route.params.id), data_page);
    dataOverview.value = data.items;
    Object.assign(data_pagination, { current: data.page, total: data.total_count });
    if (data.items && data.items.length) {
      const keys = Object.keys(data.items[0]).sort();
      const cloumn: ColumnType[] = [];
      keys.forEach((key) => {
        const description = detail.fields.find((item) => item.field_name === key)?.field_description;
        // @ts-expect-error
        cloumn.push({ title: key, dataIndex: key, description });
      });
      // @ts-expect-error
      dataColumns.value = [...cloumn, { title: '操作', dataIndex: 'operation', width: '120px' }];
    }
    dataLoading.value = false;
  };
  const handleBack = () => {
    router.back();
  };
  const handleInsert = () => {
    visible.value = true;
    isInsert.value = true;
    if (detail.fields) {
      resetDataItem(detail.fields);
    }
  };
  const handlePublish = async () => {
    await publishDataset(String(route.params.id));
    message.success('发布成功');
    await getOverview();
  };
  const delDataItem = async (index: number) => {
    await deleteDatasetItems(String(route.params.id), index);
    message.success('已删除');
    await getItems();
  };

  const confirmDelete = async () => {
    await deleteDataset(detail.id);
    message.success('已删除');
    delVisible.value = false;
    router.push('/dataset');
  };
  const editDataField = (record: Fields) => {
    updateFieldVisible.value = true;
    Object.assign(currentDataField, { ...record, prev_field_name: record.field_name });
  };
  const delDataField = async (name: string) => {
    await delDatasetField(String(route.params.id), name);
    message.success('已删除');
    getOverview();
    getItems();
  };
  const confirmExport = async () => {
    const data = await exportDataset(String(route.params.id), export_format.value);
    const { download_url } = data;
    window.open(download_url);
    exportVisible.value = false;
  };
  const editDataItem = (record: Record<string, string | number>, index: number) => {
    isInsert.value = false;
    visible.value = true;
    currentIndex.value = index;
    Object.assign(currentDataItem, record);
  };
  const handleComfirm = async () => {
    await formRef.value.validateFields();
    dataLoading.value = true;
    if (isInsert.value) {
      await insertDatasetItems(String(route.params.id), currentDataItem);
      message.success('已插入');
    } else {
      await updateDatasetItems(String(route.params.id), currentIndex.value, currentDataItem);
      message.success('已编辑');
    }
    dataLoading.value = false;
    visible.value = false;
    await getItems();
  };
  const handleComfirmUpdateField = async () => {
    await fieldFormRef.value.validateFields();
    console.log(currentDataField);
    await updateDatasetField(String(route.params.id), currentDataField);
    message.success('已编辑');
    updateFieldVisible.value = false;
    getOverview();
  };
  const toggleTable = (_pagination: TablePaginationConfig) => {
    let { current, pageSize } = _pagination;
    Object.assign(data_pagination, { current, pageSize });
    Object.assign(data_page, { page: current, limit: pageSize });
    getItems();
  };

  const toggleFieldsTable = (_pagination: TablePaginationConfig) => {
    let { current, pageSize } = _pagination;
    Object.assign(fields_pagination, { current, pageSize });
    Object.assign(fields_page, { page: current, limit: pageSize });
    // getItems();
    getFieldsList();
  };
  onMounted(async () => {
    await getOverview();
    await getItems();
  });
</script>

<template>
  <div>
    <div class="header">
      <LeftOutlined @click="handleBack" />
      <di class="m-l-10px leading-20px">
        <span class="text-20px">{{ detail.name }}</span>
        <span class="text-#797979 text-12px ml-10px">{{ detail.id }}</span>
      </di>
    </div>
    <div class="overview mt-10px">
      <a-descriptions size="small" :column="5">
        <a-descriptions-item label="发布状态">
          <a-tag :color="publish_status.find((item) => item.value === detail.publish_status)?.color">{{
            publish_status.find((item) => item.value === detail.publish_status)?.label
          }}</a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="数据量">{{ detail.data_quantity }}</a-descriptions-item>
        <a-descriptions-item label="数据类型">
          {{ datasetTypes.find((item) => item.value === detail.type)?.label }}</a-descriptions-item
        >
        <a-descriptions-item label="大小">{{ detail.file_size }}</a-descriptions-item>
        <a-descriptions-item label="创建时间">{{ convertIsoTimeToLocalTime(detail.created_at) }}</a-descriptions-item>
      </a-descriptions>
      <div class="flex justify-between items-center mb-5px">
        <div v-ellipse-tooltip.bottom class="summary">数据集简介：{{ detail.summary || '--' }}</div>
        <div class="operate">
          <a-button v-if="activeKey === 'data'" :disabled="permission" @click="handleInsert">插入</a-button>
          <a-button @click="exportVisible = true">导出</a-button>
          <a-button type="primary" :disabled="permission" @click="handlePublish">发布</a-button>
          <a-button
            :disabled="route.query.source === 'all' ? false : !isAdministrator"
            danger
            @click="delVisible = true"
            >删除</a-button
          >
        </div>
      </div>
    </div>
    <a-tabs v-model:active-key="activeKey" type="card">
      <a-tab-pane key="data" tab="数据预览">
        <a-table
          :data-source="dataOverview"
          :columns="dataColumns"
          :loading="dataLoading"
          :pagination="data_pagination"
          :scroll="{ y: 490 }"
          @change="toggleTable"
        >
          <template #headerCell="{ column }">
            <template v-if="column.description">
              {{ column.title }}
              <a-popover title="" trigger="hover">
                <template #content>
                  {{ column.description }}
                </template>
                <InfoCircleOutlined />
              </a-popover>
            </template>
          </template>
          <template #bodyCell="{ column, record, text, index }">
            <div v-if="column.dataIndex === 'operation'" class="operation-box">
              <template v-if="!permission">
                <a @click="editDataItem(record, index)">编辑</a>
                <a-popconfirm title="确定删除该数据吗?" @confirm="delDataItem(index)">
                  <a class="del-btn">删除</a>
                </a-popconfirm>
              </template>
            </div>
            <div v-else>
              {{
                text === undefined || text === null ? '--' : typeof text === 'number' ? scientificToDecimal(text) : text
              }}
            </div>
          </template>
        </a-table>
      </a-tab-pane>
      <a-tab-pane key="fields" tab="字段预览">
        <a-table
          :data-source="fieldsOverview"
          :columns="fieldsColumns"
          :pagination="fields_pagination"
          :scroll="{ y: 490 }"
          @change="toggleFieldsTable"
        >
          <template #bodyCell="{ column, record, text }">
            <div v-if="column.dataIndex === 'operation'" class="operation-box">
              <template v-if="!permission">
                <a v-if="detail.type === 'legacy-ml'" @click="editDataField(record)">编辑</a>
                <a-popconfirm title="确定删除该数据吗?" @confirm="delDataField(record.field_name)">
                  <a v-if="detail.type === 'legacy-ml' || !record.required" class="del-btn">删除</a>
                </a-popconfirm>
              </template>
            </div>
            <div v-else-if="column.dataIndex === 'required'">{{ text ? '必填' : '非必填' }}</div>
            <div v-else>{{ text || '--' }}</div>
          </template>
        </a-table>
      </a-tab-pane>
    </a-tabs>
  </div>
  <a-modal v-model:open="visible" width="50%" centered :title="isInsert ? '插入数据' : '编辑数据'" @ok="handleComfirm">
    <a-form ref="formRef" layout="vertical" autocomplete="off" :model="currentDataItem">
      <a-form-item
        v-for="item in detail.fields"
        :key="item.field_name"
        :label="item.field_name"
        :name="item.field_name"
        :rules="[{ required: item.required }]"
      >
        <template v-if="['float', 'int'].includes(item.field_type)">
          <a-input-number
            v-model:value="currentDataItem[item.field_name]"
            style="width: 100%"
            :step="item.field_type === 'int' ? 1 : 0.00000000000001"
            placeholder="请输入"
          />
        </template>
        <template v-if="item.field_type === 'string'">
          <a-input v-model:value="currentDataItem[item.field_name]" style="width: 100%" placeholder="请输入" />
        </template>
      </a-form-item>
    </a-form>
  </a-modal>
  <a-modal v-model:open="updateFieldVisible" width="50%" centered title="编辑字段" @ok="handleComfirmUpdateField">
    <a-form ref="fieldFormRef" layout="vertical" autocomplete="off" :model="currentDataField">
      <a-form-item label="字段名称" name="field_name" :rules="[{ required: true }]">
        <a-input v-model:value="currentDataField.field_name" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="字段类型" name="field_type" :rules="[{ required: true }]" placeholder="请选择">
        <a-select v-model:value="currentDataField.field_type" style="width: 100%">
          <a-select-option value="string">string</a-select-option>
          <a-select-option value="float">float</a-select-option>
          <a-select-option value="int">int</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="字段限制" name="required" :rules="[{ required: true }]" placeholder="请选择">
        <a-select v-model:value="currentDataField.required" style="width: 100%">
          <a-select-option :value="true">必填</a-select-option>
          <a-select-option :value="false">非必填</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="字段备注" name="field_description" placeholder="请选择">
        <a-textarea
          v-model:value="currentDataField.field_description"
          allow-clear
          show-count
          :maxlength="500"
          :auto-size="{ minRows: 2, maxRows: 6 }"
          placeholder="请输入字段备注"
        />
      </a-form-item>
    </a-form>
  </a-modal>
  <a-modal v-model:open="delVisible" centered :title="`确定删除数据集“${detail.name}”？`" @ok="confirmDelete">
    <p>
      数据集已被用于模型训练或评测任务，删除可能导致任务失败或数据不一致，请确保相关任务已完成或备份数据后再进行操作。确定删除数据集吗？
    </p>
  </a-modal>

  <a-modal v-model:open="exportVisible" centered title="导出数据" @ok="confirmExport">
    <a-form>
      <a-form-item label="导出格式">
        <a-radio-group v-model:value="export_format" :options="exportFormatOptions" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<style scoped lang="less">
  .header {
    display: flex;
  }
  .summary {
    width: 80%;
    overflow: hidden; /* 超出部分隐藏 */
    white-space: nowrap; /* 强制不换行 */
    text-overflow: ellipsis; /* 超出部分显示省略号 */
  }
  .operate {
    button {
      margin: 0 5px;
    }
  }
</style>
