<script setup lang="ts">
  import { ref, reactive, computed, watch, onMounted, onUnmounted } from 'vue';
  import { useRouter, useRoute, type RouteLocationNormalized, type NavigationGuardNext } from 'vue-router';
  import { LeftOutlined, InfoCircleOutlined, LoadingOutlined, PlusOutlined } from '@ant-design/icons-vue';
  import Accordion from './Components/Accordion.vue';
  import type { Rule } from 'ant-design-vue/es/form';
  import { message, type UploadChangeParam, type UploadProps } from 'ant-design-vue';
  import type { ICreateKnowledgedb, IKnowledgedb } from '@/interface/knowledge';
  import { uploadImages } from '@/api/exploration';
  import { addKnowledgeFiles, createKnowledgedb, fetchKnowledgedbDetail } from '@/api/knowledgebase';
  import { deepEqual } from '@/utils/common';
  const router = useRouter();
  const route = useRoute();
  const formRef = ref();
  const isExport = computed(() => route.params.id);
  interface IDefaultCloseUpDStatus {
    basic: boolean;
    export: boolean;
    vector: boolean;
  }

  const defaultCloseUpDStatus: IDefaultCloseUpDStatus = {
    basic: true,
    export: true,
    vector: true,
  };
  const upStatus = reactive({
    ...defaultCloseUpDStatus,
  });
  const loading = ref(false);
  const confirmLoading = ref(false);
  const hasUnsavedChanges = computed(() =>
    route.params.id ? !deepEqual(DEFAULT_FORM_STATE.files, formState.files) : !deepEqual(DEFAULT_FORM_STATE, formState),
  );
  const showConfirmModal = ref(false);
  const pendingNavigation = ref<RouteLocationNormalized>();
  let isHandlingPopState = false;
  const fileList = ref<UploadProps[]>([]);
  const fileTypes = [
    { label: '文本型', value: 'json', desc: '根据上传的文本文件直接进行切分处理' },
    { label: '表格型', value: 'table', desc: '读取表格中的文本信息，按行构建知识切片。更适用于含有长文的表格' },
  ];
  const file_type = ref('json');
  // 最大上传文件数
  const maxCount = ref(5);
  const DEFAULT_FORM_STATE: ICreateKnowledgedb = {
    name: '',
    description: '',
    embedding_model: 'bge-m3',
    files: [],
  };
  const formState = reactive<ICreateKnowledgedb>(JSON.parse(JSON.stringify(DEFAULT_FORM_STATE)));

  const closeUpStatus = (
    key: keyof typeof defaultCloseUpDStatus,
    notClose: (keyof typeof defaultCloseUpDStatus)[] = [],
    childKeys: string[] = [],
  ) => {
    const temp = { ...defaultCloseUpDStatus };
    if (typeof temp[key] == 'boolean') {
      temp[key] = !upStatus[key];
    } else {
      //子项套多个子项
      if (childKeys)
        childKeys.map((e) => {
          if (!Object.keys(temp[key]).includes(e))
            // @ts-expect-error
            temp[key][e] = true; //赋默认值
          // @ts-expect-error
          else temp[key][e] = !temp[key][e];
        });
    }
    //忽略父项
    for (const key of notClose) {
      temp[key] = true;
    }
    Object.assign(upStatus, temp);
  };
  const validatorName = (_rule: Rule, value: string) => {
    if (value == '' || value.trim().length == 0) {
      return Promise.reject('请输入知识库名称');
    }
    if (value.length > 50) {
      return Promise.reject('模型名称最多输入 50 个字');
    }
    return Promise.resolve();
  };
  const validatorFiles = () => {
    if (!fileList.value.length) {
      return Promise.reject('请上传文件');
    }
    return Promise.resolve();
  };

  // @ts-expect-error
  const beforeUpload = (file: UploadProps['fileList'][number]) => {
    const validExtensions =
      file_type.value === 'json'
        ? ['.doc', '.txt', '.docx', '.pdf', '.ppt', '.pptx', '.md']
        : ['.xlsx', '.xls', '.csv'];
    const fileExtension = file.name.substring(file.name.lastIndexOf('.')).toLowerCase();

    // 检查文件扩展名是否有效
    if (!validExtensions.includes(fileExtension)) {
      message.error(`不支持的文件格式：${file.name}`);
      return false;
    }

    // 定义文件大小限制规则
    const sizeLimits: { [key: string]: number } = {
      '.txt': 10, // 最大 10MB
      '.md': 10,
      '.xlsx': 100, // 最大 100MB
      '.xls': 100,
      '.csv': 100,
      '.pdf': 500, // 最大 500MB
      default: 50, // 默认最大 50MB
    };

    // 获取当前文件的大小限制
    const maxSize = sizeLimits[fileExtension] || sizeLimits.default;

    // 检查文件大小
    const isWithinSizeLimit = file.size / 1024 / 1024 <= maxSize;
    if (!isWithinSizeLimit) {
      message.error(`上传的文件太大，最大不能超过${maxSize}MB！`);
      return false;
    }
    return true;
  };

  const customRequest = async (options: { file: File; onSuccess: () => void; onError: () => void }) => {
    const { file, onSuccess, onError } = options;
    if (formState.files.length >= maxCount.value) {
      message.error(`上传的文件数量达到限制，最多上传${maxCount.value}个文件！`);
      return false;
    }
    const formData = new FormData();
    formData.append('files', file);
    loading.value = true;
    try {
      const res: { url: string }[] = await uploadImages(formData);
      formState.files.push({ file_name: file.name, url: res[0].url });
      loading.value = false;
      onSuccess();
      console.log(fileList.value);
    } catch {
      onError();
    }
  };
  // @ts-expect-error
  const handleRemove = (file: UploadProps['fileList'][number]) => {
    const index = fileList.value.indexOf(file);
    if (index !== -1) {
      fileList.value.splice(index, 1); // 从 fileList 中删除文件
      const custom_index = formState.files.findIndex((item) => item.file_name === file.name);
      formState.files.splice(custom_index, 1);
      message.success(`${file.name} 文件已删除`);
    }
  };
  // const handleChange = ({ fileList: newFileList }: { fileList: UploadProps[] }) => {
  const handleChange = ({ file, fileList: newFileList }: UploadChangeParam) => {
    const fileExtension = file.name.substring(file.name.lastIndexOf('.')).toLowerCase();
    // 定义文件大小限制规则
    const sizeLimits: { [key: string]: number } = {
      '.txt': 10, // 最大 10MB
      '.md': 10,
      '.xlsx': 100, // 最大 100MB
      '.xls': 100,
      '.csv': 100,
      '.pdf': 500, // 最大 500MB
      default: 50, // 默认最大 50MB
    };

    // 获取当前文件的大小限制
    const maxSize = sizeLimits[fileExtension] || sizeLimits.default;
    // @ts-expect-error
    fileList.value = newFileList.filter((file) => file.size! / 1024 / 1024 <= maxSize);
  };
  const confirmAdd = async () => {
    console.log(formState.files);

    await formRef.value.validateFields();
    confirmLoading.value = true;
    try {
      if (isExport.value) {
        await addKnowledgeFiles({
          db_id: String(route.params.id),
          files: formState.files.filter((item) => item.url).slice(0, maxCount.value),
        });
      } else {
        const params: ICreateKnowledgedb = {
          ...formState,
          files: formState.files.filter((item) => item.url).slice(0, maxCount.value),
        };
        await createKnowledgedb(params);
      }
      confirmLoading.value = false;
      message.success(isExport.value ? '已导入' : '已创建');
      Object.assign(formState, JSON.parse(JSON.stringify(DEFAULT_FORM_STATE)));
      isExport.value ? router.push(`/knowledge/file/${route.params.id}`) : router.push('/knowledge');
    } catch {
      confirmLoading.value = false;
    }
  };

  const getDetail = async () => {
    const data: IKnowledgedb = await fetchKnowledgedbDetail(String(route.params.id));
    const { name, description, embedding_model } = data;
    Object.assign(formState, { name, description, embedding_model });
  };

  watch(
    () => isExport.value,
    () => {
      if (isExport.value) {
        getDetail();
      }
    },
    { deep: true, immediate: true },
  );

  const handleBack = () => {
    // router.push(isExport.value ? `/knowledge/file/${route.params.id}?name=${route.query.name}` : '/knowledge');
    router.back();
  };
  // 用户确认离开
  const confirmNavigation = () => {
    showConfirmModal.value = false;
    Object.assign(formState, JSON.parse(JSON.stringify(DEFAULT_FORM_STATE)));
    router.push(isExport.value ? `/knowledge/file/${route.params.id}?name=${route.query.name}` : '/knowledge');
  };

  // 用户取消离开
  const cancelNavigation = () => {
    showConfirmModal.value = false;
    // 如果是popstate触发的，URL已经变化，需要恢复
    if (isHandlingPopState) {
      router.push(router.currentRoute.value.fullPath);
    }
    pendingNavigation.value = undefined;
  };

  // 监听 beforeunload 事件
  const handleBeforeUnload = (event: BeforeUnloadEvent): string | undefined => {
    if (hasUnsavedChanges.value) {
      // 如果有录入数据，显示浏览器自带的确认弹窗
      const message = '您有未保存的数据，确定要离开吗？';
      event.returnValue = message; // 兼容性处理
      return message;
    }
  };

  // 拦截回退事件
  const handleNavigation = (to: RouteLocationNormalized, _: RouteLocationNormalized, next: NavigationGuardNext) => {
    if (isHandlingPopState) {
      isHandlingPopState = false;
      next();
      return;
    }
    if (hasUnsavedChanges.value) {
      showConfirmModal.value = true;
      pendingNavigation.value = to;
      next(false); // 中止当前导航
    } else {
      next();
    }
  };
  // 处理浏览器后退/前进按钮
  const handlePopState = () => {
    if (hasUnsavedChanges.value) {
      isHandlingPopState = true;
      showConfirmModal.value = true;
      // 设置目标路由
      pendingNavigation.value = {
        path: isExport.value ? `/knowledge/file/${route.params.id}?name=${route.query.name}` : '/knowledge',
      } as RouteLocationNormalized;
    }
  };
  onMounted(() => {
    // 添加路由守卫
    const removeGuard = router.beforeEach(handleNavigation);
    // 监听popstate事件
    window.addEventListener('popstate', handlePopState);
    // 监听浏览器刷新事件
    window.addEventListener('beforeunload', handleBeforeUnload);
    // 初始历史记录
    // window.history.pushState({ id: Date.now() }, '');

    onUnmounted(() => {
      removeGuard();
      window.removeEventListener('popstate', handlePopState);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    });
  });
</script>

<template>
  <div class="h-100% flex flex-col">
    <!-- shadow-[0px_0px_10px_0px_rgba(0,0,0,0.5)] -->
    <div class="header text-18px">
      <LeftOutlined @click="handleBack" />
      <span class="m-l-10px">{{ isExport ? '导入文件' : '创建知识库' }}</span>
    </div>
    <div class="content ml-20px overflow-scroll">
      <a-form
        ref="formRef"
        autocomplete="off"
        :layout="isExport ? 'horizontal' : 'vertical'"
        :model="formState"
        v-bind="{
          labelCol: { span: 2 },
          wrapperCol: { span: isExport ? 21 : 15 },
        }"
      >
        <Accordion :expend="upStatus.basic" :expand-click="() => closeUpStatus('basic')" :bordered="false">
          <template #title>
            <div class="title-text">基本信息</div>
          </template>
          <template #content>
            <a-form-item label="知识库名称" name="name" :rules="[{ required: !isExport, validator: validatorName }]">
              <div v-if="isExport">{{ formState.name }}</div>
              <a-input
                v-else
                v-model:value="formState.name"
                style="width: 100%"
                show-count
                :maxlength="50"
                placeholder="请输入知识库名称"
              />
            </a-form-item>
            <a-form-item label="知识库描述" name="description">
              <div v-if="isExport" class="description">{{ formState.description || '--' }}</div>
              <a-textarea
                v-else
                v-model:value="formState.description"
                allow-clear
                show-count
                :maxlength="500"
                :auto-size="{ minRows: 2, maxRows: 6 }"
                placeholder="请输入知识库描述"
              />
            </a-form-item>
          </template>
        </Accordion>
        <Accordion :expend="upStatus.vector" :expand-click="() => closeUpStatus('vector')">
          <template #title>
            <div class="title-text">托管切片</div>
          </template>
          <template #content>
            <a-form-item name="embedding_model" :rules="[{ required: true, message: '请选择向量模型' }]">
              <template #label>
                <span style="margin-right: 10px">向量模型</span>
                <a-tooltip>
                  <template #title
                    >将文本转化为用数值表示的向量形式。用于知识导入时的向量转化，以及知识问答过程的语义向量检索</template
                  >
                  <InfoCircleOutlined class="span-mg-left" />
                </a-tooltip>
              </template>
              <a-select
                v-model:value="formState.embedding_model"
                placeholder="请选择向量模型"
                style="width: 300px"
                disabled
              >
                <!-- <a-select-option value="bge-reranker-v2-m3">bge-reranker-v2-m3</a-select-option> -->
                <a-select-option value="bge-m3">bge-m3</a-select-option>
              </a-select>
            </a-form-item>
          </template>
        </Accordion>

        <Accordion :expend="upStatus.export" :expand-click="() => closeUpStatus('export')">
          <template #title>
            <div class="title-text">导入文件</div>
          </template>
          <template #content>
            <a-form-item label="文件类型" :rules="[{ required: true, message: '请选择文件类型' }]">
              <div class="types">
                <div
                  v-for="item in fileTypes"
                  :key="item.value"
                  class="types-items"
                  :class="{ active: item.value === file_type }"
                  @click="file_type = item.value"
                >
                  <div class="flex-col">
                    <div class="font-bold">{{ item.label }}</div>
                    <div class="c-#7f7f7f font-size-14px">{{ item.desc }}</div>
                  </div>
                  <div v-if="item.value === file_type" class="checkbox">
                    <a-checkbox :checked="true"></a-checkbox>
                  </div>
                </div>
              </div>
            </a-form-item>

            <a-form-item label="选择文件" name="fileList" :rules="[{ required: true, validator: validatorFiles }]">
              <a-upload-dragger
                v-model:fileList="fileList"
                name="file"
                :multiple="true"
                :max-count="maxCount"
                :accept="file_type === 'json' ? '.doc,.txt,.docx,.pdf,.ppt,.pptx,.md' : '.xlsx,.xls,.csv'"
                :beforeUpload="beforeUpload"
                :customRequest="customRequest"
                @remove="handleRemove"
                @change="handleChange"
              >
                <loading-outlined v-if="loading"></loading-outlined>
                <plus-outlined v-else></plus-outlined>
                <div class="ant-upload-text">将文件拖放此处，或点击上传</div>
                <div class="ant-upload-text text-#797979 p-x-10px">
                  {{
                    file_type === 'json'
                      ? `单次上传文档数量为 ${maxCount} 个；支持 .doc/.txt/.docx/.pdf/.ppt/.pptx/.md 七种格式；.txt/.md 文件不能超过10MB，.pdf文件不能超过 500MB ，其他类型文件不能超过 50MB；暂不支持图片数据的处理 。`
                      : `单次上传文档数量不超过 ${maxCount} 个；支持 .xlsx/.xls/.csv三种文件格式；单个文件大小不超过 100MB；仅支持UTF-8 编码格式。`
                  }}
                </div>
              </a-upload-dragger>
            </a-form-item>
          </template>
        </Accordion>
      </a-form>
    </div>
    <div class="footer border-t-0 shadow-[0_-4px_6px_-1px_rgb(0,0,0,0.1)]">
      <a-button type="primary" style="margin-right: 10px" :loading="confirmLoading" @click="confirmAdd">确定</a-button>
      <a-button @click="handleBack">取消</a-button>
    </div>
  </div>
  <a-modal
    v-model:open="showConfirmModal"
    centered
    title="确定离开当前页面？"
    @ok="confirmNavigation"
    @cancel="cancelNavigation"
  >
    <p class="text-#797979">离开后将不保留原有内容</p>
  </a-modal>
</template>

<style scoped lang="less">
  .header {
    height: 40px;
    height: 52px;
    line-height: 40px;
    border-bottom: 1px solid #ccc;
    padding-bottom: 12px;
  }
  .content {
    height: calc(100% - 100px);
  }
  .types {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    padding: 10px;
    cursor: pointer;
    border-radius: 2px;

    .types-items {
      position: relative;
      display: flex;
      flex: 1 1 50%;
      max-width: calc(50% - 5px);
      padding: 10px;
      background-color: #fff;
      border: 1px solid #ccc;
      border-radius: 6px;
    }

    .checkbox {
      position: absolute;
      top: 2px;
      right: 4px;
      width: 16px;
      height: 16px;
    }

    .active {
      border: 1px solid #1677ff;
    }
  }
  .footer {
    padding: 10px;
  }
  .description {
    word-wrap: break-word; /* 强制在单词内部换行 */
    white-space: normal; /* 允许文本正常换行 */
    padding-top: 5px;
  }

  .title-text {
    font-weight: bold;
  }
</style>
