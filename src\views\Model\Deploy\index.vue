<script setup lang="ts">
  import { ref, reactive, watch, onMounted } from 'vue';
  import { ConfigPackage } from '@/components';
  import { Tooltip } from 'ant-design-vue';
  import { QuestionCircleFilled } from '@ant-design/icons-vue';
  import {
    createDeployTask,
    createTrainDeployTask,
    createP2lDeployTask,
    createOCRDeployTask,
    checkDeployName,
    deploySklearnTask,
    createBlenderDeployTask,
  } from '@/api/deploy';
  import { getRandomCharacters, makeK8sNameValid } from '@/utils/common';
  import { sourceEnum } from '@/utils/enum';
  import type { ICreateDeployTaskProps } from '@/interface/deploy';
  import { useRoute, useRouter } from 'vue-router';
  import { deployMethod } from '@/views/Deploy/index';
  import { VAceEditor } from 'vue3-ace-editor';
  import 'ace-builds/src-noconflict/theme-monokai.js';
  import 'ace-builds/src-noconflict/mode-sh.js';
  import type { IAceOptions } from '@/interface';
  import type { DataItem, IModelFormState, IVllm } from '@/interface/model';
  import type { Rule } from 'ant-design-vue/es/form/interface';
  import type { ISGLang, IDatasetParameter } from '@/interface/model';
  import { GPUList, sklearn_numeric_features_map } from '@/views/Model/Train/index';
  import { machineLearningList } from '../Manage';
  interface IProps {
    model: Partial<IModelFormState>;
    closeVisible: () => void;
  }
  const props = defineProps<IProps>();
  const inferenceEngine = ref<string[]>([]);
  interface IDefaultCloseUpDStatus {
    method: boolean;
    setting: boolean;
    datasource: boolean;
    command: boolean;
    dataset: boolean;
    childform: Record<string, boolean>;
  }
  const defaultCloseUpDStatus: IDefaultCloseUpDStatus = {
    method: true,
    setting: false,
    datasource: true,
    command: false,
    dataset: false,
    childform: reactive<Record<string, boolean>>({
      command: true,
      docker: true,
    }),
  };
  const route = useRoute();
  const router = useRouter();
  const upStatus = reactive({
    ...defaultCloseUpDStatus,
  });
  const loading = ref(false);
  const deployRef = ref();
  const deployType = [{ label: '标准机型', value: 0, desc: '支持标准机型单机部署' }];
  interface IFormState {
    deploy_method: string;
    model_id: string;
    gpu_number_list?: string;
    category?: string;
    finetuning_type: number;
    service_name: string;
    introduction: string;
    container_count: number;
    run_command: string;
    docker_image_id: string;
    dataset_parameter: IDatasetParameter;
  }
  const formState = reactive<IFormState>({
    deploy_method: '',
    model_id: '',
    gpu_number_list: undefined,
    category: 'llm',
    finetuning_type: 0,
    service_name: '',
    introduction: '',
    container_count: 1,
    run_command: '',
    docker_image_id: '',
    dataset_parameter: { name: undefined, type: 'system', numeric_features: [], numeric_labels: [] },
  });

  const state = reactive<{ dockerList: { id: string; name: string }[]; docker: string; dockerType: number }>({
    dockerList: [],
    docker: 'reg.shukeyun.com:9088/algorithm/ai-agent-vllm:v2',
    dockerType: 0,
  });

  const aceOptions: IAceOptions = {
    enableBasicAutocompletion: true,
    enableSnippets: true,
    enableLiveAutocompletion: true,
    tabSize: 4,
    enableEmmet: true,
    fontSize: 15,
    showPrintMargin: false, // 去除编辑器里的竖线
    highlightActiveLine: true,
    useWorker: true,
    readOnly: false,
  };
  const resources = ref<DataItem[]>([{ key: '1', GPU: '1 * NVIDIA V100', CPU: 'Dynamic', memory: 'Dynamic' }]);
  const selectedRowKeys = ref<(string | number)[]>(['1']);
  const getCheckboxProps = (record: DataItem) => {
    return {
      disabled: record.disabled,
    };
  };
  const rowSelection = reactive({
    checkStrictly: false,
    type: 'radio',
    getCheckboxProps: getCheckboxProps,
    selectedRowKeys,
    onChange: (keys: (string | number)[], selectedRows: DataItem[]) => {
      selectedRowKeys.value = keys;
      const items: DataItem[] = JSON.parse(JSON.stringify(selectedRows));
      items.forEach((i) => {
        delete i.key;
        delete i.disabled;
      });
      resources.value = items;
    },
  });
  const resourceColumn = [
    {
      title: 'GPU',
      dataIndex: 'GPU',
      key: 'GPU',
    },
    {
      title: 'CPU',
      dataIndex: 'CPU',
      key: 'CPU',
    },
    {
      title: '内存',
      dataIndex: 'memory',
      key: 'memory',
    },
  ];

  const closeUpStatus = (
    key: keyof typeof defaultCloseUpDStatus,
    notClose: (keyof typeof defaultCloseUpDStatus)[] = [],
    childKeys: string[] = [],
  ) => {
    const temp = { ...defaultCloseUpDStatus };
    if (typeof temp[key] == 'boolean') {
      // @ts-expect-error
      temp[key] = !upStatus[key];
    } else {
      //子项套多个子项
      if (childKeys)
        childKeys.map((e) => {
          if (!Object.keys(temp[key]).includes(e))
            // @ts-expect-error
            temp[key][e] = true; //赋默认值
          // @ts-expect-error
          else temp[key][e] = !temp[key][e];
        });
    }
    //忽略父项
    for (const key of notClose) {
      // @ts-expect-error
      temp[key] = true;
    }
    Object.assign(upStatus, temp);
  };

  const onConfirm = async () => {
    await deployRef.value.validateFields();
    loading.value = true;

    if (!formState.run_command.includes('API_PORT=8000')) {
      formState.run_command = formState.run_command.replace(/API_PORT=\d{4}/, `API_PORT=8000`);
    }
    const newformState = JSON.parse(JSON.stringify(formState));
    const params: ICreateDeployTaskProps = {
      ...newformState,
      model_id: formState.model_id,
      resource: resources.value,
    };
    if (formState.run_command) {
      params.run_command = formState.run_command.split('\n').map((e: string) => e.trim());
    }
    if (['blender', 'p2l', ...machineLearningList].includes(formState.category!)) {
      // delete params.resource;
      // @ts-expect-error
      delete params.finetuning_type;
      delete params.container_count;
      delete params.run_command;
    }
    if (newformState.gpu_number_list !== undefined) {
      params.gpu_number_list = [newformState.gpu_number_list];
    }
    try {
      // route.query.category === 'trained' || route.path === '/my-model'
      //   ? machineLearningList.includes(formState.category!)
      //     ? await deploySklearnTask(params)
      //     : await createTrainDeployTask(params)
      //   : ['multimodal', 'llm'].includes(formState.category!)
      //     ? await createDeployTask(params)
      //     : await createOCRDeployTask(params);

      if (route.query.category === 'trained' || route.path === '/my-model') {
        if (machineLearningList.includes(formState.category!)) {
          await deploySklearnTask(params);
        } else if (formState.category === 'p2l') {
          await createP2lDeployTask(params);
        } else {
          await createTrainDeployTask(params);
        }
      } else {
        if (['multimodal', 'llm'].includes(formState.category!)) {
          await createDeployTask(params);
        }
        if (formState.category! === 'ocr') {
          await createOCRDeployTask(params);
        }
        if (formState.category! === 'blender') {
          await createBlenderDeployTask(params);
        }
      }
      router.push({ path: '/deploy' });
    } catch {}
    loading.value = false;
  };

  const getRunCommand = () => {
    const { output_name, category, name, inference_engine } = props.model;
    const _name = output_name || name;
    let command = '';
    switch (category) {
      case 'llm':
      case 'multimodal':
        if (formState.deploy_method === 'vllm') {
          const { max_model_len, gpu_memory_utilization } = inference_engine!.vllm as IVllm;
          command = `vllm
  serve
  --gpu-memory-utilization ${gpu_memory_utilization}
  --max_model_len ${max_model_len}
  --enable-auto-tool-choice
  --tool-call-parser hermes`;
        } else if (formState.deploy_method === 'transformers') {
          command = `CUDA_VISIBLE_DEVICES=0 API_PORT=8000 llamafactory-cli api --model_name_or_path /ckpt/${_name} --template default`;
        } else if (formState.deploy_method === 'sglang') {
          const toolFuncTemp = (inference_engine!.sglang as ISGLang).tool_func_tmp;
          const mem_fraction_static = (inference_engine!.sglang as ISGLang).mem_fraction_static;
          command = `sglang.launch_server --tool-call-parser ${toolFuncTemp} --mem-fraction-static ${mem_fraction_static}`;
        } else {
          command = '';
        }
        break;
      case 'gotocr':
        command = 'python /app/run_gotocr.py';
        break;
      case 'paddleocr':
        command = 'python /app/run_paddle.py';
        break;
      case 'ocr':
        command = 'python /app/run_ocr.py';
        break;
      default:
        command = '';
    }
    return command;
  };
  watch(
    [() => props.model],
    async ([model]) => {
      if (model) {
        const {
          source_name,
          output_name,
          category,
          inference_engine,
          docker_images,
          output_model_id,
          name,
          id,
          dataset_parameter,
        } = model;
        // 回显配置好的镜像
        state.dockerList = docker_images!;
        formState.docker_image_id = docker_images?.length ? docker_images[0].id : '';

        inferenceEngine.value = Object.keys(inference_engine || {});
        const _name = output_name || source_name || name;
        formState.category = category;
        if (route.query.category === 'trained') {
          formState.model_id = ['p2l', ...machineLearningList].includes(category!) ? id! : output_model_id!;
        } else {
          formState.model_id = model.id!;
        }
        if (machineLearningList.includes(formState.category!)) {
          // @ts-expect-error
          formState.dataset_parameter = dataset_parameter;
        }
        formState.deploy_method = inferenceEngine.value[0];
        formState.service_name = `${makeK8sNameValid(`${_name}_${getRandomCharacters()}`)}`;
        formState.run_command = getRunCommand();
        /**
             * docker run -d
              -v /data/public/ckpt/${_name}:/ckpt/${_name}
              -v /data/public/dataset:/dataset
              --name ${formState.service_name}${state.dockerList.length ? `\n${state.dockerList[0].name}` : ''}
              bash -c "CUDA_VISIBLE_DEVICES=0 API_PORT=8000 llamafactory-cli api
              --model_name_or_path /ckpt/${_name}
              --template default
              --finetuning_type lora"
            *
            */

        // if (route.query.category === 'trained') {
        //   formState.run_command = `vllm serve /ckpt/${_name} --gpu-memory-utilization 0.1 --max_model_len 4096 --enforce-eager`;
        /**
         * docker run -d
          -v /data/public/dataset:/dataset
          --name ${formState.service_name}${state.dockerList.length ? `\n${state.dockerList[0].name}` : ''}
          bash -c "CUDA_VISIBLE_DEVICES=0 API_PORT=8000 llamafactory-cli api
          --model_name_or_path /ckpt/${_name}
          --template default
          --finetuning_type lora"
         */
        // }
      }
    },
    { deep: true, immediate: true },
  );

  const validatorName = async (_rule: Rule, value: string) => {
    if (value == '' || value.trim().length == 0) {
      return Promise.reject('请输入服务名称');
    }
    const regex = /^[a-z0-9][a-z0-9\-.]*[a-z0-9]$/;
    if (!regex.test(value)) {
      return Promise.reject('格式不正确：仅支持小写字母、数字、-和.，且首尾必须为字母或数字');
    }
    if (value.length > 63) {
      return Promise.reject('服务名称最多输入 63 个字');
    }
    try {
      await checkDeployName(value.trim());
    } catch (e) {
      if (e === 'AlreadyExists') {
        return Promise.reject('该名称已存在，请重新命名');
      }
      return Promise.reject(e);
    }
    return Promise.resolve();
  };
  onMounted(async () => {});

  const runChang = (e: Event, key: string) => {
    if (key == 'docker')
      formState.run_command = formState.run_command.replace(
        /^reg.shukeyun.*$/m,
        // @ts-expect-error
        state.dockerList.find((n) => n.id == e)?.name,
      );
    if (key == 'service_name')
      formState.run_command = formState.run_command.replace(/^--name.*$/m, `--name ${formState.service_name}`);
  };
</script>

<template>
  <div class="flex flex-col h-100%">
    <div class="flex-1 overflow-scroll container">
      <a-form ref="deployRef" :model="formState" name="basic" autocomplete="off" layout="vertical">
        <ConfigPackage
          v-if="formState.category === 'llm'"
          :expand="upStatus.method"
          :expand-click="() => closeUpStatus('method')"
          label="部署方式"
        >
          <div class="flex-nowrap flex overflow-x-auto whitespace-nowrap overflow-scroll p-x-10px">
            <div
              v-for="item in deployMethod.filter((item) => inferenceEngine.includes(item.value))"
              :key="item.value"
              class="radio-button-wrappe flex p-10px"
              :class="{ actived: formState.deploy_method === item.value }"
              @click="
                formState.deploy_method = item.value;
                formState.finetuning_type = 0;
                formState.run_command = getRunCommand();
              "
            >
              <span class="mr-5px">{{ item.label }}</span>
              <Tooltip>
                <template #title>
                  {{ item.desc }}
                </template>
                <QuestionCircleFilled />
              </Tooltip>
            </div>
          </div>
          <div class="types">
            <template v-for="item in deployType" :key="item.value">
              <div
                class="types-items"
                :class="{ active: formState.finetuning_type === item.value }"
                @click="formState.finetuning_type = item.value"
              >
                <div class="flex-col">
                  <div class="font-bold">{{ item.label }}</div>
                  <div class="c-#7f7f7f font-size-14px">{{ item.desc }}</div>
                </div>
                <div v-if="formState.finetuning_type === item.value" class="checkbox">
                  <a-checkbox :checked="true"></a-checkbox>
                </div>
              </div>
            </template>
          </div>
        </ConfigPackage>
        <ConfigPackage
          v-if="route.query.model_category === 'sklearn' || machineLearningList.includes(props.model.category!)"
          :expand="upStatus.dataset"
          :expand-click="() => closeUpStatus('dataset')"
          label="数据集配置"
        >
          <a-row :style="{ marginBottom: '10px' }">
            <a-col :span="6">
              <a-select v-model:value="formState.dataset_parameter.type" disabled style="width: 100%">
                <a-select-option value="system">公开数据集</a-select-option>
                <!-- <a-select-option value="private">自研数据集</a-select-option> -->
              </a-select>
            </a-col>
            <a-col :span="18">
              <div class="dataset">
                <a-form-item
                  label=""
                  :name="['dataset_parameter', 'name']"
                  :rules="[{ required: true, message: '请选择数据集' }]"
                  :style="{ marginBottom: '0px' }"
                >
                  <a-select
                    v-model:value="formState.dataset_parameter.name"
                    disabled
                    style="width: 100%"
                    placeholder="请选择数据集"
                  >
                    <a-select-option value="IndividualCreditRatingForm">
                      <div>IndividualCreditRatingForm</div>
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </div>
            </a-col>
          </a-row>
          <a-form-item
            label="数值特征"
            :name="['dataset_parameter', 'numeric_features']"
            :rules="[{ required: true, message: '请选择数值特征' }]"
            :style="{ marginBottom: '0px' }"
          >
            <a-select
              v-model:value="formState.dataset_parameter.numeric_features"
              mode="multiple"
              disabled
              style="width: 100%"
              placeholder="请选择数值特征"
            >
              <a-select-option
                v-for="item in sklearn_numeric_features_map[formState.category!]"
                :key="item.value"
                :value="item.value"
              >
                <div>{{ item.label }}</div>
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item
            label="数值标签"
            :name="['dataset_parameter', 'numeric_labels']"
            :rules="[{ required: true, message: '请选择数值标签' }]"
            :style="{ marginBottom: '0px' }"
          >
            <a-select
              v-model:value="formState.dataset_parameter.numeric_labels"
              disabled
              style="width: 100%"
              placeholder="请选择数值标签"
            >
              <a-select-option
                v-for="item in sklearn_numeric_features_map[formState.category!]"
                :key="item.value"
                :value="item.value"
              >
                <div>{{ item.label }}</div>
              </a-select-option>
            </a-select>
          </a-form-item>
        </ConfigPackage>
        <ConfigPackage :expand="upStatus.setting" :expand-click="() => closeUpStatus('setting')" label="部署设置">
          <a-form-item
            label="服务名称"
            name="service_name"
            :rules="[{ required: true, validator: validatorName, trigger: 'change' }]"
          >
            <a-input
              v-model:value="formState.service_name"
              placeholder="请输入服务名称"
              show-count
              :maxlength="63"
              @change="(e: Event) => runChang(e, 'service_name')"
            />
          </a-form-item>
          <a-form-item label="服务简介" name="introduction">
            <a-textarea v-model:value="formState.introduction" placeholder="请输入版本描述" :rows="4" />
          </a-form-item>
        </ConfigPackage>
        <ConfigPackage
          :expand="upStatus.datasource"
          :expand-click="() => closeUpStatus('datasource')"
          label="部署资源配置"
        >
          <ConfigPackage
            v-if="!machineLearningList.includes(formState.category!)"
            :expand="upStatus.childform['resourceList']"
            :expand-click="() => closeUpStatus('childform', ['datasource'], ['resourceList'])"
            label="资源配置"
            spacious
          >
            <a-form-item label="容器数" name="container_count" :rules="[{ required: true, message: '请输入容器数' }]">
              <a-input-number
                v-model:value="formState.container_count"
                disabled
                :min="1"
                :style="{ width: '100%' }"
                placeholder="请输入"
              ></a-input-number>
            </a-form-item>
            <a-form-item label="GPU 显卡" name="gpu_number_list" :rules="[{ required: true }]">
              <a-select v-model:value="formState.gpu_number_list" placeholder="请选择" style="width: 100%">
                <a-select-option v-for="n in GPUList" :key="n.value" :value="n.value">{{ n.label }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="资源列表" :rules="[{ required: true }]">
              <a-table
                :data-source="sourceEnum"
                :columns="resourceColumn"
                :row-selection="rowSelection"
                row-key="key"
                :pagination="false"
              />
            </a-form-item>
          </ConfigPackage>
          <ConfigPackage
            :expand="upStatus.childform['docker']"
            :expand-click="() => closeUpStatus('childform', ['datasource'], ['docker'])"
            label="镜像配置"
            spacious
          >
            <a-form-item label="镜像类型">
              <a-select v-model:value="state.dockerType" style="width: 100%">
                <a-select-option :value="0">官方镜像</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="镜像选择" name="docker_image_id" :rules="[{ required: true, message: '请选择镜像' }]">
              <a-select
                v-model:value="formState.docker_image_id"
                style="width: 100%"
                @change="(e: Event) => runChang(e, 'docker')"
              >
                <a-select-option v-for="n in state.dockerList" :key="n.id" :value="n.id">{{ n.name }}</a-select-option>
              </a-select>
            </a-form-item>
          </ConfigPackage>
          <ConfigPackage
            v-if="!['blender', ...machineLearningList].includes(formState.category!)"
            :expand="upStatus.childform['command']"
            :expand-click="() => closeUpStatus('childform', ['datasource'], ['command'])"
            label="初始命令"
            tip="与容器相关的初始命令"
            spacious
          >
            <v-ace-editor
              v-model:value="formState.run_command"
              lang="sh"
              theme="monokai"
              :options="aceOptions"
              style="height: 300px; border: 1px solid #dbd3d3"
            />
          </ConfigPackage>
        </ConfigPackage>
      </a-form>
    </div>
    <div class="h-36px mt-10px flex flex-justify-end">
      <a-button type="primary" :loading="loading" @click="onConfirm">确定</a-button>
    </div>
  </div>
</template>

<style scoped lang="less">
  @import url('../Train/index.less');
</style>
