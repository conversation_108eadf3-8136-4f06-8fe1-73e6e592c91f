<script setup lang="ts">
  import { ref } from 'vue';

  const props = defineProps<{
    visible?: boolean;
    handleCloseModal?: () => void;
  }>();

  const emit = defineEmits(['close', 'agree']);

  const isOpenModal = ref(false);

  // 链接协议弹窗相关
  const agreementModalVisible = ref(false);
  const agreementModalTitle = ref('');
  const agreementContent = ref('');

  // 协议内容数据
  const agreementData = {
    service: {
      title: '服务条款',
      content: `一、服务条款

  1. 服务说明
  本服务条款是您与我们之间关于使用本平台服务的法律协议。通过使用本服务，您同意遵守本条款的所有规定。

  2. 服务内容
  我们提供 AI 智能对话、嵌入代码生成等相关服务。服务内容可能会根据业务发展需要进行调整。

  3. 用户义务
  - 您应当合法使用本服务，不得用于任何违法违规目的
  - 您应当保护好自己的账户信息，对账户下的所有活动负责
  - 您不得恶意攻击、破坏本服务的正常运行

  4. 服务限制
  - 我们有权根据实际情况对服务进行维护、升级或暂停
  - 对于免费服务，我们不承诺服务的持续性和稳定性

  5. 知识产权
  本服务中的所有内容，包括但不限于文字、图片、音频、视频、软件等，均受知识产权法保护。

  6. 免责声明
  在法律允许的最大范围内，我们对因使用本服务而产生的任何直接或间接损失不承担责任。

  7. 条款变更
  我们有权随时修改本服务条款，修改后的条款将在平台上公布。继续使用服务即表示您接受修改后的条款。`,
    },
    user: {
      title: '用户协议',
      content: `一、用户协议

  1. 协议范围
  本用户协议适用于所有使用本平台服务的用户。注册或使用本服务即表示您同意本协议的全部内容。

  2. 账户注册
  - 您应当提供真实、准确的注册信息
  - 您应当及时更新注册信息，确保信息的有效性
  - 一个手机号码或邮箱只能注册一个账户

  3. 账户安全
  - 您应当妥善保管账户密码，不得向他人透露
  - 如发现账户被盗用，应立即通知我们
  - 您对账户下的所有操作承担责任

  4. 使用规范
  - 不得发布违法、有害、威胁、辱骂、骚扰、侵权的内容
  - 不得传播垃圾信息、广告信息或恶意软件
  - 不得干扰或破坏服务的正常运行

  5. 内容权利
  - 您对自己发布的内容拥有知识产权
  - 您授权我们在提供服务过程中使用您的内容
  - 我们有权删除违规内容

  6. 服务变更
  我们有权随时修改、暂停或终止服务，并会提前通知用户。

  7. 违约处理
  如您违反本协议，我们有权采取警告、限制功能、暂停或终止服务等措施。

  8. 争议解决
  因本协议产生的争议，双方应友好协商解决；协商不成的，提交有管辖权的人民法院解决。`,
    },
    privacy: {
      title: '隐私条款',
      content: `一、隐私条款

  1. 隐私保护承诺
  我们高度重视用户隐私保护，本隐私条款说明我们如何收集、使用、存储和保护您的个人信息。

  2. 信息收集
  我们可能收集以下信息：
  - 注册信息：用户名、邮箱、手机号码等
  - 使用信息：登录记录、操作日志、设备信息等
  - 内容信息：您在使用服务过程中产生的对话内容、上传的文件等

  3. 信息使用
  我们收集信息的目的：
  - 提供和改进服务
  - 用户身份验证和账户安全
  - 客户服务和技术支持
  - 产品分析和优化

  4. 信息共享
  我们不会向第三方出售、出租或交易您的个人信息，除非：
  - 获得您的明确同意
  - 法律法规要求
  - 保护我们的合法权益

  5. 信息存储
  - 我们采用行业标准的安全措施保护您的信息
  - 信息存储在安全的服务器中，采用加密技术
  - 我们会定期审查和更新安全措施

  6. 信息访问和控制
  您有权：
  - 访问和更新您的个人信息
  - 删除您的账户和相关信息
  - 选择接收或拒绝营销信息

  7. Cookie 使用
  我们使用 Cookie 来改善用户体验，您可以通过浏览器设置控制 Cookie 的使用。

  8. 未成年人保护
  我们不会故意收集未满 18 岁用户的个人信息。如发现，我们会立即删除相关信息。

  9. 隐私条款更新
  我们可能会更新本隐私条款，更新后会在平台上公布。继续使用服务即表示您接受更新后的条款。

  10. 联系我们
  如您对隐私保护有任何问题，请通过平台客服联系我们。`,
    },
  };

  const openModal = () => {
    isOpenModal.value = true;
  };

  defineExpose({
    openModal,
  });

  const handleOk = () => {
    isOpenModal.value = false;
    emit('agree');
    emit('close');
  };

  const handleCancel = () => {
    isOpenModal.value = false;
    emit('close');
  };

  // 处理协议链接点击
  const handleAgreementClick = (type: 'service' | 'user' | 'privacy') => {
    const agreement = agreementData[type];
    agreementModalTitle.value = agreement.title;
    agreementContent.value = agreement.content;
    agreementModalVisible.value = true;
  };

  // 关闭协议弹窗
  const handleAgreementClose = () => {
    agreementModalVisible.value = false;
  };
</script>

<template>
  <a-modal
    :open="isOpenModal"
    :footer="null"
    :width="900"
    :mask-closable="false"
    centered
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <template #title>
      <!-- <ExclamationCircleOutlined style="margin-right: 8px" /> -->
      使用者承诺须知
    </template>
    <div class="user-rights-notice">
      <div class="notice-content">
        <p class="notice-text">
          本平台所有功能均受到知识产权法等相关法律平台（下称"本平台"）使用相关工具且代理
          管理您的作品，您同意并授权本平台为您在知识产权方面提供以下内容：
        </p>

        <p class="notice-text">
          • 您有为用户在本平台上上传、发布的作品，应当具备、完整的知识产权，不得侵犯 他人知识产权或其他任何权利；
        </p>

        <p class="notice-text">
          • 您在使用本平台上上传、发布时，应当保证国家法律、法规、遵守公共秩
          序、善良风俗，不得利用、传播利益、暴力、恐怖、迷信等违法和不良信息，不得实施违法犯罪活
          动等违法要求，如有违反，一经本平台发现或接到举报后经审核属实的，本平台有权立即删除相关
          下架、禁止上传内容，封禁违规账号等处理方式，如造成损失的，您应当承担全部责任；
        </p>

        <p class="notice-text">
          • 若您上传作品涉及他人肖像的（包括但不限于手绘、文字、声音、音频、图片、
          动画等）您应当获得第三方权利，本平台为您代为申请相关权限及权限对象等对象等
          的合法授权，若您违反上述规定导致纠纷的，您应当承担全部责任，与本平台无关，且应当赔偿本平台因 此遭受的损失；
        </p>

        <p class="notice-text">
          • 请勿擅自他人肖像权等相关权利，请您遵守相关法律法规，请您遵守伦理，或任何中华人民
          共和国法律法规的内容，我们有权拒绝所有内容并带来相关损失，以及可能造成的任何损失，
          及可能造成的任何损失，请您遵守相关法律法规的内容；
        </p>

        <p class="notice-text">
          • 更多信息请参见
          <a class="link" @click="handleAgreementClick('user')">《用户协议》</a>
          <a class="link" @click="handleAgreementClick('privacy')">《隐私条款》</a>。
        </p>
      </div>

      <div class="notice-footer">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleOk">我已知晓，同意</a-button>
      </div>
    </div>
    <!-- 协议详情弹窗 -->
    <a-modal
      v-model:open="agreementModalVisible"
      :title="agreementModalTitle"
      :width="800"
      :style="{ height: '700px', top: '50px',bottom: '50px'}"
      :footer="null"
      class="agreement-modal"
      @cancel="handleAgreementClose"
    >
      <div class="agreement-content">
        <div class="agreement-text">{{ agreementContent }}</div>
      </div>
    </a-modal>
  </a-modal>
</template>

<style lang="less" scoped>
  .user-rights-notice {
    .notice-content {
      max-height: 400px;
      overflow-y: auto;
      padding: 16px 0;

      .notice-text {
        font-size: 14px;
        line-height: 1.6;
        color: #333;
        margin-bottom: 12px;
        text-align: justify;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .link-text {
        color: #1890ff;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }
    }

    .notice-footer {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      margin-top: 24px;
      padding-top: 16px;
      border-top: 1px solid #f0f0f0;
    }
  }

  /* 协议弹窗样式 */
  .agreement-modal :deep(.ant-modal-header) {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 24px;
  }

  .agreement-modal :deep(.ant-modal-body) {
    padding: 24px;
    max-height: 60vh;
    overflow-y: auto;
  }

  .agreement-content {
    font-size: 14px;
    line-height: 1.6;
  }

  .agreement-text {
    color: #262626;
    white-space: pre-line;
  }
</style>
