import type { ICreateTaskProps, IFetchSklearnTaskRequest, IModelFormState, IModelItemResponse, IOutputModelReq } from '@/interface/model';
import { getEnv } from '@/utils/getEnv';
import request from '@/utils/request';

const { VITE_APP_V2_BASE_URL } = getEnv();

const V2 = VITE_APP_V2_BASE_URL;


export function fetchModelList(data: {
  page: number;
  limit: number;
  name?: string;
  tag?: string;
  operations?: null;
  published_at?: string[];
}) {
  // return request.$Axios.post('/model/latest_list', data);
  return request.$Axios.post(`/model/version/latest/list[${V2}]`, data);
}
export function fetchModelListTag(data: {
  page: number;
  limit: number;
  name: string;
  operations: string[];
  tags: string[];
  published_at?: string[] | null;
}) {
  return request.$Axios.post('/model/published_latest_list', data);
}
export function getModelInfo(oid: string) {
  return request.$Axios.get(`/model/published/${oid}`);
}
// 基础模型下拉列表
export function modelCodeList() {
  return request.$Axios.get(`/model/source_filter_list`);
}
export function tagTreeList() {
  return request.$Axios.get(`/model/tag_gather`);
}

//添加模型
export function addModel(data: {
  tags: string[],
  model_source_id: string | number;
  name: string;
  size?: number;
  seq: string; // 排序
  operations: string[]; // 支持的操作
  description: string;
}) {
  return request.$Axios.post(`/model`, data);
}

//修改模型
export function editModel(data: { id: string; description: string; detail: string }) {
  return request.$Axios.post(`/model/version/update/${data.id}[${V2}]`, data);
}
//模型上下线 
export function serveModel(data: { operation: string; oid: string }) {
  const { operation, oid } = data
  return request.$Axios.put(`/model/published/${operation}/${oid}`, {});
}

//删除模型
export function delModel(id: string) {
  return request.$Axios.del(`/model/published/${id}`, { id });
}

// 创建任务
export const createTask = (data: ICreateTaskProps) => {
  return request.$Axios.post(`/train/create/task`, data);
};
// 创建机器学习任务
export const createSklearnTask = (data: ICreateTaskProps) => {
  return request.$Axios.post(`/sklearn_train/task/create`, data);
};
// 创建机器学习任务
export const createP2lTask = (data: ICreateTaskProps) => {
  return request.$Axios.post(`/train/create/p2l/task`, data);
};




// 任务列表
export const getTaskList = (data: { page: number; limit: number }) => {
  return request.$Axios.get(`/train/task_list`, data);
};

// sklearn 训练列表
export const getSklearnTaskList = (data: IFetchSklearnTaskRequest) => {
  return request.$Axios.get(`/sklearn_train/task/list`, data);
}

// 任务详情
export function getTaskInfo(oid: string) {
  return request.$Axios.get(`/train/task/${oid}`);
}
// 获取sklearn训练详情
export function getSklearnTaskInfo(task_id: string) {
  return request.$Axios.get(`/sklearn_train/task/detail/${task_id}`);
}


// 获取任务日志
export function getTaskLogs(oid: string) {
  return request.$Axios.get(`/train/task/logs/${oid}`);
}
// 获取任务日志
export function getSKlearnTaskLogs(task_id: string) {
  return request.$Axios.get(`/sklearn_train/task/logs/${task_id}`);
}

// 删除任务
export const deleteTask = (id: string) => {
  return request.$Axios.del(`/train/task`, { id });
};
// 停止任务
export const stopTask = (id: string) => {
  return request.$Axios.post(`/train/task/${id}/stop`);
};

// 训练服务操作
export const taskOperation = (data: { opration: 'stop' | 'restart' | 'rm'; oid: string }) => {
  return request.$Axios.put(`/train/task/${data.opration}/${data.oid}`, data);
};


// 获取容器系统资源
export const getSystemResource = (data: { id: string, start_time: string, end_time: string, time_unit: number }) => {
  const { id, start_time, end_time, time_unit } = data
  return request.$Axios.get(`/train/task/system_resource/${id}`, { start_time, end_time, time_unit });
};

// 获取模型指标监控数据
export const getModelMonitor = (oid: string) => {
  return request.$Axios.get(`/train/task/monitor_metric/${oid}`)
}
// 获取sklearn模型指标监控数据
export const getSklearnModelMonitor = (task_id: string) => {
  return request.$Axios.get(`/sklearn_train/task/metrics_data/${task_id}`)
}

// 错误码调试
export const testRespCode = (data: { resp_code: number, resp_msg: string }) => {
  return request.$Axios.post(`/test/test_resp_code`, data)
}

// v2 获取模型下拉列表
export const modelCodeListV2 = () => {
  return request.$Axios.get(`/model/model_source_filter_list[${V2}]`)
}
// v2 添加模型
export const addModelV2 = (data: IModelFormState) => {
  return request.$Axios.post(`/model/version/add[${V2}]`, data)
}
// v2 编辑添加模型
export const editModelV2 = (data: IModelItemResponse) => {
  return request.$Axios.post(`/model/version/update/${data.id}[${V2}]`, data)
}
// v2 获取模型详情
export function getModelInfoV2(oid: string) {
  return request.$Axios.get(`/model/detail/${oid}[${V2}]`);
}
export function getModelManageInfoV2(oid: string) {
  return request.$Axios.get(`/model/version/detail/${oid}[${V2}]`);
}
// v2 发布模型
export function publishModelV2(data: { version_id: string; }) {
  const { version_id } = data
  return request.$Axios.post(`/model/version/publish/${version_id}[${V2}]`);
}
// v2 获取模型广场列表

export function fetchModelListV2(data: {
  page: number;
  limit: number;
  name: string;
  operations: string[];
  tags: string[];
  published_at?: string[] | null;
}) {
  return request.$Axios.post(`/model/list[${V2}]`, data);
}

export function deleteModelV2(version_id: string) {
  return request.$Axios.post(`/model/version/delete/${version_id}[${V2}]`);
}

// 检查训练名称是否重复
export const checkTrainName = (name: string) => {
  return request.$Axios.post(`/train/name/check`, { name });
};

// 检查模型名称是否重复
export const checkModelName = (name: string) => {
  return request.$Axios.post(`/model/version_name/check[${V2}]`, { name });
};
// 检查训练输出名称是否重复
export const checkTarinOutputName = (name: string) => {
  return request.$Axios.post(`/train/output_name/check`, { name });
};
// 检查模型名称是否重复
export const checkSklearnModelName = (name: string) => {
  return request.$Axios.post(`/sklearn_train/name/check`, { name });
};
// 检查训练输出名称是否重复
export const checkSKlearnTarinOutputName = (name: string) => {
  return request.$Axios.post(`/sklearn_train/output_name/check`, { name });
};


// 我的输出模型
export const getOutputModelList = (data: IOutputModelReq) => {
  return request.$Axios.get(`/output_model/list`, data);
};

export const deleteOutputModel = (output_model_id: string) => {
  return request.$Axios.post(`/output_model/delete/${output_model_id}`);
};

export const getOutputModelDetail = (output_model_id: string) => {
  return request.$Axios.get(`/output_model/detail/${output_model_id}`);
}

export const deleteSklearnTask = (task_id: string) => {
  return request.$Axios.put(`/sklearn_train/task/delete/${task_id}`);
}
export const stopSklearnTask = (task_id: string) => {
  return request.$Axios.put(`/sklearn_train/task/stop/${task_id}`);
}