<script lang="ts" setup>
  import { ref } from 'vue';
  import Chat from './Chat/index.vue';
  import Speakers from './Sound/index.vue';
  import DigitalImage from './DigitalImage/index.vue';
  // import Work from './work/index.vue';

  const activeKey = ref('1');
  const speakersRef = ref(); // 新增ref
  // const generationRef = ref();

  const handleTabChange = (key: string) => {
    // 如果当前离开声音tab，暂停声音
    if (activeKey.value === '2' && speakersRef.value && typeof speakersRef.value.pauseAudio === 'function') {
      speakersRef.value.pauseAudio();
    }
    activeKey.value = key;
    console.log(key, 'key');
    // if (key === '2' && generationRef.value?.hasUnsaved()) {
    //   activeKey.value = '1';
    //   Modal.confirm({
    //     title: '确定离开当前页面吗？',
    //     okText: '确定离开',
    //     cancelText: '取消',
    //     onOk: () => {
    //       activeKey.value = '2';
    //     },
    //     onCancel: () => {
    //       activeKey.value = '1';
    //     },
    //   });
    // } else {
    //   activeKey.value = key;
    // }
  };
</script>

<template>
  <a-tabs v-model:active-key="activeKey" class="tab-container" :tab-bar-gutter="0" @change="handleTabChange">
    <a-tab-pane key="1" tab="数字人形象">
      <DigitalImage v-if="activeKey === '1'" ref="generationRef" />
    </a-tab-pane>
    <a-tab-pane key="2" tab="声音">
      <Speakers v-if="activeKey === '2'" ref="speakersRef" />
    </a-tab-pane>
    <a-tab-pane key="3" tab="数字人对话">
      <Chat v-if="activeKey === '3'" />
    </a-tab-pane>
  </a-tabs>
</template>

<style lang="less" scoped>
  .tab-container {
    height: 100%;
    // :deep(.ant-tabs-nav) {
    //   margin-bottom: 0 !important;
    // }
    :deep(.ant-tabs-nav::before) {
      right: -12px !important;
      left: -12px !important;
      position: absolute;
      border-bottom: 1px solid rgba(5, 5, 5, 0.06);
      content: '';
    }
    :deep(.ant-tabs-tab) {
      padding: 8px 0 !important;
      > .ant-tabs-tab-btn {
        text-align: center;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 500;
        font-size: 16px !important;
        color: #afb0b3 !important;
        line-height: 22px;
        font-style: normal;
      }
    }
    :deep(.ant-tabs-tab-active > .ant-tabs-tab-btn) {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 16px !important;
      color: #17181a !important;
      line-height: 22px;
      text-align: left;
      font-style: normal;
    }
    :deep(.ant-tabs-tab) {
      margin: 0 30px 0px 18px;
    }
  }
</style>
