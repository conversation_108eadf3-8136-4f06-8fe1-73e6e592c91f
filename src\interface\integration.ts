import type { IPage } from ".";

export interface ISearchState {
  name?: string;
  method?: string;
}

export interface IFetchIntegrationList extends IPage {
  name?: string;
  method?: string;
}

export interface ICreateIntegration {
  name: string;
  svc_id_list: string[];
  master_svc_id: string;
  config?: IIntegrationConfig
}

export interface IIntegrationConfig {
  fusion_model?: string;
  sort_model?: string;
  top_k: number
}

export interface IIntegrationItem {
  name: string;
  method: string;
  svc_id_list: number[];
  config: IIntegrationConfig;
  id: string;
  creator_id: string;
  updater_id: null;
  created_at: string;
  updated_at: string;
  deleted_at: string;
}

export interface IIntegrationDetail extends Omit<IIntegrationItem, 'svc_id_list'> {
  svc_list: number[];
}

export interface IIntegrationTaskResponse {
  model?: string;
  content: string;
  svc: string;
  created_at: string
}
export interface IIntegrationHistoryItem {
  id: string;
  ensemble_id: string;
  question: string;
  candidates: ICandidates[];
  status: string;
  final_result: ICandidates;
  error_msg: string;
  created_at: string;
  updated_at: string;
  deleted_at: string;
}

export interface ICandidates {
  model: string;
  content: string;
  svc: string
}