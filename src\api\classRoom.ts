import request from '@/utils/request';
import { getEnv } from '@/utils';

const { VITE_APP_AVATAR_URL } = getEnv();

const AVATAR = VITE_APP_AVATAR_URL;

// 定义宽松的对象类型
type TLooseObject = Record<string, unknown>;
//获取课程/项目
export function getCourse(params?: TLooseObject) {
  return request.$Axios.get(`/virtual-classroom-service/video/generate/project/[${AVATAR}]`, params);
}
//新增项目
export function addProject(data: any) {
  return request.$Axios.post(`/virtual-classroom-service/video/generate/project/[${AVATAR}]`, data);
}
//删除项目
export function delProject(ids: any) {
  return request.$Axios.del(`/virtual-classroom-service/video/generate/project/[${AVATAR}]`, ids);
}
//修改项目
export function updateProject(data: any) {
  return request.$Axios.put(`/virtual-classroom-service/video/generate/project/[${AVATAR}]`, data);
}
//添加分镜
export function addLens(data: any) {
  return request.$Axios.post(`/virtual-classroom-service/video/project/images/[${AVATAR}]`, data);
}
//修改分镜
export function updateLens(data: any) {
  return request.$Axios.put(`/virtual-classroom-service/video/project/images/[${AVATAR}]`, data);
}
//删除分镜
export function delLens(ids: any) {
  return request.$Axios.del(`/virtual-classroom-service/video/project/images/[${AVATAR}]`, ids);
}
//删除课程
export function delCourse(ids: any) {
  return request.$Axios.del(`/virtual-classroom-service/video/generate/[${AVATAR}]`, ids);
}
//合成视频
export function synthesisVideo(data: any) {
  return request.$Axios.post(`/virtual-classroom-service/video/generate/[${AVATAR}]`, data);
}

//上传课件
export function uploadFile(data: any, config?: any) {
  return request.$Axios.post(`/virtual-classroom-service/video/upload/file/[${AVATAR}]`, data, config);
}
//上传图片
export function uploadImg(data: any, config?: any) {
  return request.$Axios.post(`/virtual-classroom-service/video/upload/images/[${AVATAR}]`, data, config);
}
//获取素材图片列表
export function getMaterial(data: any) {
  return request.$Axios.get(`/virtual-classroom-service/video/upload/images/[${AVATAR}]`, data);
}
//删除素材
export function delMaterial(data: any) {
  return request.$Axios.del(`/virtual-classroom-service/video/upload/images/[${AVATAR}]`, data);
}

//解析PPT
export function analysisPPT(data: any) {
  return request.$Axios.post(`/virtual-classroom-service/video/parse/file/[${AVATAR}]`, data);
}
//AI生成文案
export function aiCopywriting(data: any) {
  return request.$Axios.post(`/virtual-classroom-service/video/parse/images/[${AVATAR}]`, data);
}
//AI生成问答
export function aiAnswer(data: any) {
  return request.$Axios.post(`/virtual-classroom-service/qa/generate/[${AVATAR}]`, data);
}
//添加答疑
export function addAnswer(data: any) {
  return request.$Axios.post(`/virtual-classroom-service/qa/query/[${AVATAR}]`, data);
}
//查询答疑
export function getAnswer(data: any) {
  return request.$Axios.get(`/virtual-classroom-service/qa/generate/[${AVATAR}]`, data);
}

//修改答疑
export function updateAnswer(data: any) {
  return request.$Axios.put(`/virtual-classroom-service/qa/generate/[${AVATAR}]`, data);
}
//删除答疑
export function delAnswer(data: any) {
  return request.$Axios.del(`/virtual-classroom-service/qa/generate/[${AVATAR}]`, data);
}
 
//试听（旧接口：https://dev-gcluster,shukeyun.com/algorithm/virtual-classroom-service/lecturer/tts/）
export function tts(data: any) {
  return request.$Axios.post(`/virtual-classroom-service/lecturer/tts/[${AVATAR}]`, data);
}
// 复制并编辑
export function copyEdit(data: any) {
  return request.$Axios.post(`/virtual-classroom-service/video/project/duplicate/[${AVATAR}]`, data);
}
