<script setup lang="ts">
  import { ref, reactive, onMounted, nextTick, watch, onUnmounted } from 'vue';
  import { CustomForm } from '@/components';
  import type { IFormItem, IPagination } from '@/interface';
  import { TABLE_PAGINATION } from '@/json/common';
  import { TrainStatus, STATE_MAP } from '.';
  import type { ColumnType, TablePaginationConfig } from 'ant-design-vue/es/table/interface';
  import { useRoute, useRouter } from 'vue-router';
  import { deleteSklearnTask, getSklearnTaskList, stopSklearnTask, taskOperation } from '@/api/model';
  import { message } from 'ant-design-vue';
  import { formatTime, convertIsoTimeToLocalTime } from '@/utils/common';

  const route = useRoute();
  const router = useRouter();
  const DEFAULT_SEARCHSTATE = {
    task_name: undefined,
    model_name: undefined,
    status: undefined,
  };
  const searchState = reactive({ ...DEFAULT_SEARCHSTATE });
  const formConfig: IFormItem[] = [
    {
      field: 'task_name',
      type: 'input',
      label: '任务名称',
      placeholder: '请输入',
    },
    {
      field: 'model_name',
      type: 'input',
      label: '模型名称',
      placeholder: '请输入',
    },
    {
      field: 'status',
      type: 'select',
      label: '状态',
      placeholder: '请选择',
      options: TrainStatus,
    },
  ];
  const columns: ColumnType[] = [
    { title: '任务名称', dataIndex: 'task_name', fixed: 'left' },
    { title: '任务描述', dataIndex: 'description', width: 200, ellipsis: true },
    { title: '输出训练模型', dataIndex: 'output_name' },
    { title: '输出模型id', dataIndex: 'output_model_id', width: 200 },
    { title: '任务最大运行时间', dataIndex: 'max_run_time', width: 200 },
    { title: '训练数据集', dataIndex: 'dataset' },
    // { title: '训练参数', dataIndex: 'train_parameter' },
    { title: '状态', dataIndex: 'status' },
    { title: '任务运行时间', dataIndex: 'run_time', width: 200 },
    { title: '模型名称', dataIndex: 'model_name' },
    { title: '镜像名称', dataIndex: 'docker_image' },
    { title: '创建时间', dataIndex: 'created_at' },
    { title: '更新时间', dataIndex: 'updated_at' },
    { title: '操作', dataIndex: 'operation', width: 240, fixed: 'right' },
  ];
  const pagination = reactive({ ...TABLE_PAGINATION });
  const page: IPagination = reactive({ page_index: 1, page_size: 10 });
  const loading = ref(false);
  const dataSource: Record<string, string>[] = reactive([]);
  const sklearnTime = ref();
  const tableHeight = ref(0);
  const stopLoad = ref(false);
  const statusState = reactive<{ list: boolean }>({ list: false });

  const getTaskListReq = async () => {
    loading.value = true;
    const data = await getSklearnTaskList({
      ...{ page: page.page_index, limit: page.page_size },
      ...searchState,
    });
    const { total, items: list } = data;
    dataSource.length = 0;
    dataSource.push(...list);
    Object.assign(pagination, { current: page.page_index, total: total });
    if (!data.finished) {
      if (sklearnTime.value) {
        clearInterval(sklearnTime.value);
        sklearnTime.value = null;
      }
      startTaskListRR();
    }
    Object.assign(statusState, { list: data.finished });
    loading.value = false;
  };
  const getTableHeight = () => {
    const tableItem = document.querySelector('.container');
    tableHeight.value = tableItem?.clientHeight as number;
  };

  const startTaskListRR = () => {
    sklearnTime.value = setInterval(async () => {
      if (statusState.list) {
        clearInterval(sklearnTime.value);
        sklearnTime.value = null;
        return;
      }
      const data = await getSklearnTaskList({
        ...{ page: page.page_index, limit: page.page_size },
        ...searchState,
      });
      const { total, items: list } = data;
      dataSource.length = 0;
      dataSource.push(...list);
      Object.assign(pagination, { current: page.page_index, total: total });
      Object.assign(statusState, { list: data.finished });
    }, 10000);
  };
  const jumpToDetail = (record: { model_id: string }) => {
    const { model_id } = record;
    router.push({
      path: '/model/detail',
      query: { ...route.query, modelid: model_id },
    });
  };
  const taskInfo = (record: { id: string; output_model_id: string }, type?: string) => {
    const { id, output_model_id } = record;
    router.push({
      path: '/train/detail',
      query: { ...route.query, taskid: id, type, category: 'trained', output_model_id, model_category: 'sklearn' },
    });
  };
  const toggleTable = (_pagination: TablePaginationConfig) => {
    let { current, pageSize } = _pagination;
    console.log(current, pageSize);
    Object.assign(pagination, { current, pageSize });
    Object.assign(page, { page_index: current, page_size: pageSize });
    getTaskListReq();
  };
  const deleteTaskItem = async (id: string) => {
    await deleteSklearnTask(id);
    // await taskOperation({ opration: 'rm', oid: id });
    message.success('删除成功');
    getTaskListReq();
  };
  const handleStopTask = async (id: string) => {
    // await stopTask(id);
    stopLoad.value = true;
    await stopSklearnTask(id);
    message.success('停止成功');
    stopLoad.value = false;
    getTaskListReq();
  };
  const onFinish = (values: Record<string, string>) => {
    const searchConfig: { [key: string]: string } = {};
    for (let key in values) {
      searchConfig[key] = values[key];
    }
    Object.assign(searchState, { ...searchConfig });
    page.page_index = 1;
    getTaskListReq();
  };
  const onRest = () => {
    Object.assign(pagination, { ...TABLE_PAGINATION });
    Object.assign(page, { page_index: 1, page_size: 10 });
    Object.assign(searchState, { ...DEFAULT_SEARCHSTATE });
    getTaskListReq();
  };

  onMounted(async () => {
    await nextTick();
    getTableHeight();
  });
  onUnmounted(() => {
    clearInterval(sklearnTime.value);
    sklearnTime.value = null;
  });

  watch(
    () => route.path,
    (path) => {
      if (path === '/train') {
        getTaskListReq();
      } else {
        clearInterval(sklearnTime.value);
        sklearnTime.value = null;
      }
    },
    { deep: true, immediate: true },
  );
</script>

<template>
  <template v-if="route.name === 'sklearn_task_detail'">
    <router-view></router-view>
  </template>
  <template v-else>
    <CustomForm :form-items="formConfig" @on-finish="onFinish" @on-rest="onRest" />
    <a-table
      :data-source="dataSource"
      :columns="columns"
      :pagination="pagination"
      :loading="loading"
      :scroll="{ y: tableHeight - 200, x: 'max-content' }"
      @change="toggleTable"
    >
      <template #bodyCell="{ column, record, text }">
        <div v-if="column.dataIndex === 'operation'" class="operation-box">
          <a @click="taskInfo(record)">查看</a>
          <a-button
            v-if="[STATE_MAP.RUNNING].includes(record.status)"
            :loading="stopLoad"
            type="link"
            style="padding: 0; margin-right: 10px"
            @click="handleStopTask(record.id)"
            >停止</a-button
          >
          <a v-if="record.status === STATE_MAP.COMPLETED" @click="taskInfo(record, 'deploy')">部署</a>
          <a-popconfirm
            v-if="![STATE_MAP.CREATING].includes(record.status)"
            title="确定删除这条任务吗?"
            @confirm="deleteTaskItem(record.id)"
          >
            <a class="del-btn">删除</a>
          </a-popconfirm>
        </div>
        <div v-else-if="column.dataIndex === 'dataset'">{{ record.dataset_parameter.name }}</div>
        <div v-else-if="['created_at', 'updated_at'].includes(column.dataIndex)">
          {{ convertIsoTimeToLocalTime(text) }}
        </div>
        <div v-else-if="column.dataIndex === 'model_name'">
          <a class="table-a-btn" @click="jumpToDetail(record)">{{ record.model_name }}</a>
        </div>
        <div v-else-if="column.dataIndex === 'run_time'">
          {{ formatTime(text) }}
        </div>
        <div v-else-if="column.dataIndex === 'method_parameter'">
          {{ `${record.tf_parameter.type} / ${record.tf_parameter.stage} / ${record.tf_parameter.finetuning_type}` }}
        </div>
        <div v-else-if="column.dataIndex === 'status'">
          <a-tag :color="TrainStatus.find((item) => item.value === text)?.color">{{
            TrainStatus.find((item) => item.value === text)?.label
          }}</a-tag>
        </div>
        <div v-else>{{ text || '--' }}</div>
      </template>
    </a-table>
  </template>
</template>
