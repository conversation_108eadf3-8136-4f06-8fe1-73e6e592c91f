import { message } from 'ant-design-vue';
import { getEnv } from '@/utils';
import dayjs from 'dayjs';
import 'dayjs/plugin/timezone'; // 引入时区插件
import 'dayjs/plugin/utc'; // 引入 UTC 插件import utc from 'dayjs-plugin-utc';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

// 加载插件
dayjs.extend(utc);
dayjs.extend(timezone);

/**
 * 
 * @param length 随机生成字符串的长度
 * @returns 
 */
export const getRandomCharacters = (length: number = 10) => {
  const characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    result += characters[randomIndex];
  }
  return result;
};

export const copyText = function (str: string) {
  const oInput = document.createElement('input');
  oInput.value = str;
  document.body.appendChild(oInput);
  oInput.select(); // 选择对象
  document.execCommand('Copy'); // 执行浏览器复制命令
  message.success('复制成功');
  oInput.remove();
};

export const formatTime = (seconds: number) => {
  if (seconds === null || seconds === undefined) return '--';
  const days = Math.floor(seconds / (3600 * 24)); // 计算天数
  const hours = Math.floor((seconds % (3600 * 24)) / 3600); // 计算剩余的小时数
  const minutes = Math.floor((seconds % 3600) / 60); // 计算剩余的分钟数
  const remainingSeconds = seconds % 60; // 计算剩余的秒数
  // console.log(days, hours, minutes, remainingSeconds)
  const parts: string[] = [];
  if (days > 0) parts.push(`${days}天`);
  if (hours > 0) parts.push(`${hours}时`);
  if (minutes > 0) parts.push(`${minutes}分`);
  if (remainingSeconds > 0 || parts.length === 0) parts.push(`${remainingSeconds}秒`);
  return parts.join('');
};

export const isType = (value: string, type: string) => {
  return Object.prototype.toString.call(value).includes(type);
};

// local存储
export const getLocalItem = (name: string) => {
  try {
    const value = window.localStorage.getItem(name);
    return value ? (isType(value, 'String') ? value : JSON.parse(value)) : null;
  } catch {
    return null;
  }
};

export const setLocalItem = (name: string, value: string) => {
  return window.localStorage.setItem(name, isType(value, 'String') ? value : JSON.stringify(value));
};

export const closeUpStatus = (key: string, upStatus: unknown, notClose: string[] = [], childKeys: string[] = []) => {
  // @ts-expect-error
  const temp = { ...upStatus };
  if (typeof temp[key] == 'boolean') {
    // @ts-expect-error
    temp[key] = !upStatus[key];
  } else {
    //子项套多个子项
    if (childKeys)
      childKeys.map((e: string) => {
        if (!Object.keys(temp[key]).includes(e))
          temp[key][e] = true; //赋默认值
        else temp[key][e] = !temp[key][e];
      });
  }
  //忽略父项
  for (const key of notClose) {
    // @ts-expect-error
    if (Object.keys(upStatus).includes(key)) temp[key] = true;
    else temp['childform'][key] = true;
  }
  // @ts-expect-error
  Object.assign(upStatus, temp);
};

export const goToLogin = (cas_app_id?: string, id?: string) => {
  const { VITE_CAS_HOST } = getEnv();
  const { MODE } = import.meta.env;
  const appId = cas_app_id;
  const queryObj: { appId?: string, screen_id?: string, path?: string } = { appId };
  if (id) {
    queryObj.screen_id = id;
  }
  if (MODE === 'development') {
    queryObj.path = location.origin + location.pathname;
  }
  let url = `${VITE_CAS_HOST}/#/login`;
  Object.keys(queryObj).forEach((e) => {
    // @ts-expect-error
    const value = encodeURIComponent(queryObj[e]);
    if (!url.includes('?')) {
      url += `?${e}=${value}`;
    } else {
      url += `&${e}=${value}`;
    }
  });
  location.href = url;
};

export const getHashParams = () => {
  // 获取当前 URL 的 hash 部分
  const paramsString = window.location.hash.split('?')[1];
  // 如果没有参数部分，直接返回空对象
  if (!paramsString) {
    return {};
  }
  // 分割 hash 部分为键值对
  const pairs = paramsString.split('&'); // 按 '&' 分割成多个键值对
  const params: Record<string, string> = {};

  pairs.forEach((pair) => {
    const [key, value] = pair.split('='); // 按 '=' 分割成键和值
    if (key) {
      params[decodeURIComponent(key)] = decodeURIComponent(value || ''); // 解码并存储到对象中
    }
  });

  return params;
};

export function makeK8sNameValid(name: string, maxLength: number = 63): string {
  /**
   * 将任意字符串转换为合法的 Kubernetes 名称（RFC 1123 label 格式）
   *
   * @param name - 原始名称
   * @param maxLength - 最大长度限制（默认 63，Kubernetes 对 DNS 子域名的最大限制）
   * @return 合法的名称
   */

  // 转小写
  name = name.toLowerCase();

  // 替换所有非 a-z0-9\- 的字符为 -
  name = name.replace(/[^a-z0-9\-]/g, '-');

  // 替换多个连续的 '-' 为单个 '-'
  name = name.replace(/-+/g, '-');

  // 去除开头和结尾的 '-' 或其他非法字符
  name = name.replace(/^[-]+|[-]+$/g, '');

  // 截断到最大长度（如果设置了）
  if (maxLength && name.length > maxLength) {
    name = name.slice(0, maxLength).replace(/[-]+$/g, ''); // 如果截断后以 '-' 结尾也去掉
  }

  return name;
}

export const getImageCover = (imeges: any) => {
  const empty = '';
  if (imeges.length) {
    const image = imeges.find((item: any) => !!item.image_url);
    if (image) {
      return image.image_url;
    } else {
      return empty;
    }
  } else {
    return empty;
  }
};
// 音频播放
export function playAudio(audioUrl: string): Promise<'finished'> {
  return new Promise((resolve, reject) => {
    const audio = new Audio(audioUrl);
    audio.onended = () => {
      console.log('音频播放完成');
      resolve('finished');
    };

    audio.onerror = error => {
      console.error('播放音频失败：', error);
      reject(error);
    };

    audio
      .play()
      .catch(error => {
        console.error('播放音频失败：', error);
        reject(error);
      });
  });
}

/**
 * 计算从指定时间到当前时间过去了多久
 * @param startDate 指定时间
 * @returns 
 */
export const monthsBetween = (startDate: string | Date) => {
  const start = new Date(startDate);
  const end = new Date();
  const yearsDiff = end.getFullYear() - start.getFullYear();
  const monthsDiff = end.getMonth() - start.getMonth();
  const daysDiff = end.getDate() - start.getDate();
  let totalMonths = yearsDiff * 12 + monthsDiff;
  if (totalMonths > 0) {
    return `${totalMonths}个月`;
  } else {
    return `${daysDiff || 1}天`;
  }
}
/**
 * 将 ISO 时间转换为用户所在地区的本地时间
 * @param {string} isoTime - 后端返回的 ISO 时间字符串
 * @param {string} formatStr - 时间格式
 * @returns {string} - 转换为用户所在地区的本地时间字符串
 */
export const convertIsoTimeToLocalTime = (isoTime: string, formatStr?: string = 'YYYY-MM-DD HH:mm:ss') => {
  if (!isoTime) return '--'
  const utcDate = new Date(isoTime);
  const formatter = new Intl.DateTimeFormat(undefined, {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false,
    timeZoneName: 'short'
  });
  return dayjs(formatter.format(utcDate)).format(formatStr)
}

type DeepValue = string | number | boolean | null | undefined | symbol | object | DeepValue[];

function isObject(value: unknown): value is Record<string, DeepValue> {
  return value !== null && typeof value === 'object' && !Array.isArray(value);
}

function isArray(value: unknown): value is DeepValue[] {
  return Array.isArray(value);
}

export function deepEqual(obj1: DeepValue, obj2: DeepValue): boolean {
  // 如果两者都是原始类型或 null/undefined，直接比较
  if (obj1 === obj2) {
    return true;
  }

  // 如果一个是对象，另一个不是，直接返回 false
  if (isObject(obj1) !== isObject(obj2)) {
    return false;
  }

  // 如果两者都是数组
  if (isArray(obj1) && isArray(obj2)) {
    if (obj1.length !== obj2.length) return false;
    for (let i = 0; i < obj1.length; i++) {
      if (!deepEqual(obj1[i], obj2[i])) return false;
    }
    return true;
  }

  // 如果一个是数组，另一个不是，直接返回 false
  if (isArray(obj1) || isArray(obj2)) return false;

  // 如果两者都是对象
  if (isObject(obj1) && isObject(obj2)) {
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);

    // 比较键的数量
    if (keys1.length !== keys2.length) return false;

    // 比较每个键的值
    for (const key of keys1) {
      if (!keys2.includes(key) || !deepEqual(obj1[key], obj2[key])) return false;
    }
    return true;
  }

  // 其他情况（如一个为原始类型，另一个为对象或数组）
  return false;
}

/**
 * 防抖函数
 * @param func 需要防抖的函数
 * @param wait 等待时间(毫秒)
 * @param immediate 是否立即执行
 * @returns 返回防抖后的函数
 */
export function debounce<T extends (...args: never[]) => unknown>(
  func: T,
  wait: number = 300,
  immediate: boolean = false
): (...args: Parameters<T>) => void {
  let timeoutId: ReturnType<typeof setTimeout> | null = null;

  return (...args: Parameters<T>): void => { // 使用箭头函数保留this
    const shouldCallNow = immediate && timeoutId === null;

    if (timeoutId !== null) {
      clearTimeout(timeoutId);
    }

    timeoutId = setTimeout(() => {
      timeoutId = null;
      if (!immediate) {
        func(...args); // 直接调用，不需要保存this
      }
    }, wait);

    if (shouldCallNow) {
      func(...args); // 直接调用
    }
  };
}

export function scientificToDecimal(num: number) {
  const str = num.toString();
  if (!str.includes('e')) return str; // 如果不是科学计数法，直接返回

  const [base, exponent] = str.split('e');
  const exp = parseInt(exponent, 10);
  const [integer, fraction = ''] = base.split('.');

  if (exp > 0) {
    // 正指数（如 1.23e+5 → 123000）
    const zeros = exp - fraction.length;
    return integer + fraction + (zeros > 0 ? '0'.repeat(zeros) : '');
  } else {
    // 负指数（如 4e-14 → 0.00000000000004）
    const zeros = Math.abs(exp) - integer.length;
    return '0.' + '0'.repeat(zeros) + integer + fraction;
  }
}